import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../widgets/ras_tva_checker_widget.dart';

class CalculateurSection extends StatefulWidget {
  const CalculateurSection({super.key});

  @override
  State<CalculateurSection> createState() => _CalculateurSectionState();
}

class _CalculateurSectionState extends State<CalculateurSection> {
  final _formKey = GlobalKey<FormState>();
  final _montantController = TextEditingController();
  double _selectedTaux = 20;
  bool _isHT = true;
  double? _montantHT;
  double? _montantTVA;
  double? _montantTTC;

  final List<double> _tauxTVA = [0, 10, 20];

  void _calculer() {
    if (_formKey.currentState!.validate()) {
      final montant =
          double.parse(_montantController.text.replaceAll(',', '.'));
      setState(() {
        if (_isHT) {
          _montantHT = montant;
          _montantTVA = montant * (_selectedTaux / 100);
          _montantTTC = montant * (1 + (_selectedTaux / 100));
        } else {
          _montantTTC = montant;
          _montantHT = montant / (1 + (_selectedTaux / 100));
          _montantTVA = _montantTTC! - _montantHT!;
        }
      });
    }
  }

  void _reinitialiser() {
    setState(() {
      _montantController.clear();
      _selectedTaux = 20;
      _isHT = true;
      _montantHT = null;
      _montantTVA = null;
      _montantTTC = null;
    });
  }

  @override
  void dispose() {
    _montantController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  colorScheme.primary,
                  colorScheme.primaryContainer,
                ],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: colorScheme.shadow.withOpacity(0.1),
                  blurRadius: 10,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Calculateur TVA',
                  style: textTheme.headlineMedium?.copyWith(
                    color: colorScheme.onPrimary,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'Calculez rapidement la TVA et les montants HT/TTC',
                  style: textTheme.titleMedium?.copyWith(
                    color: colorScheme.onPrimary.withOpacity(0.8),
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 24),
          Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Card(
                  elevation: 2,
                  surfaceTintColor: colorScheme.surfaceTint,
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Type de montant',
                          style: textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                            color: colorScheme.onSurface,
                          ),
                        ),
                        const SizedBox(height: 8),
                        SegmentedButton<bool>(
                          segments: const [
                            ButtonSegment<bool>(
                              value: true,
                              label: Text('Montant HT'),
                            ),
                            ButtonSegment<bool>(
                              value: false,
                              label: Text('Montant TTC'),
                            ),
                          ],
                          selected: {_isHT},
                          onSelectionChanged: (Set<bool> newSelection) {
                            setState(() {
                              _isHT = newSelection.first;
                            });
                          },
                          style: ButtonStyle(
                            backgroundColor:
                                WidgetStateProperty.resolveWith<Color>(
                              (Set<WidgetState> states) {
                                if (states.contains(WidgetState.selected)) {
                                  return colorScheme.primaryContainer;
                                }
                                return colorScheme.surface;
                              },
                            ),
                            foregroundColor:
                                WidgetStateProperty.resolveWith<Color>(
                              (Set<WidgetState> states) {
                                if (states.contains(WidgetState.selected)) {
                                  return colorScheme.onPrimaryContainer;
                                }
                                return colorScheme.onSurface;
                              },
                            ),
                          ),
                        ),
                        const SizedBox(height: 16),
                        Text(
                          'Montant',
                          style: textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                            color: colorScheme.onSurface,
                          ),
                        ),
                        const SizedBox(height: 8),
                        TextFormField(
                          controller: _montantController,
                          decoration: InputDecoration(
                            hintText: 'Saisissez le montant',
                            suffixText: 'DH',
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                            focusedBorder: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(8),
                              borderSide:
                                  BorderSide(color: colorScheme.primary),
                            ),
                            enabledBorder: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(8),
                              borderSide:
                                  BorderSide(color: colorScheme.outline),
                            ),
                            labelStyle: TextStyle(color: colorScheme.primary),
                          ),
                          keyboardType: const TextInputType.numberWithOptions(
                            decimal: true,
                          ),
                          inputFormatters: [
                            FilteringTextInputFormatter.allow(
                                RegExp(r'^\d*[,.]?\d*')),
                          ],
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'Veuillez saisir un montant';
                            }
                            if (double.tryParse(value.replaceAll(',', '.')) ==
                                null) {
                              return 'Veuillez saisir un montant valide';
                            }
                            return null;
                          },
                        ),
                        const SizedBox(height: 16),
                        Text(
                          'Taux de TVA',
                          style: textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                            color: colorScheme.onSurface,
                          ),
                        ),
                        const SizedBox(height: 8),
                        DropdownButtonFormField<double>(
                          value: _selectedTaux,
                          decoration: InputDecoration(
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                            focusedBorder: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(8),
                              borderSide:
                                  BorderSide(color: colorScheme.primary),
                            ),
                            enabledBorder: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(8),
                              borderSide:
                                  BorderSide(color: colorScheme.outline),
                            ),
                          ),
                          items: _tauxTVA.map((taux) {
                            return DropdownMenuItem<double>(
                              value: taux,
                              child: Text('$taux%'),
                            );
                          }).toList(),
                          onChanged: (value) {
                            setState(() {
                              _selectedTaux = value!;
                            });
                          },
                        ),
                        const SizedBox(height: 24),
                        Row(
                          children: [
                            Expanded(
                              child: FilledButton.icon(
                                onPressed: _calculer,
                                icon: const Icon(Icons.calculate),
                                label: const Text('Calculer'),
                              ),
                            ),
                            const SizedBox(width: 8),
                            IconButton.filledTonal(
                              onPressed: _reinitialiser,
                              icon: const Icon(Icons.refresh),
                              tooltip: 'Réinitialiser',
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
                if (_montantHT != null) ...[
                  const SizedBox(height: 24),
                  Card(
                    elevation: 2,
                    surfaceTintColor: colorScheme.surfaceTint,
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Résultat',
                            style: textTheme.titleMedium?.copyWith(
                              fontWeight: FontWeight.bold,
                              color: colorScheme.onSurface,
                            ),
                          ),
                          const SizedBox(height: 16),
                          _buildResultRow(
                            context,
                            'Montant HT:',
                            _montantHT!,
                            colorScheme.primary,
                          ),
                          Divider(color: colorScheme.outlineVariant),
                          _buildResultRow(
                            context,
                            'Montant TVA:',
                            _montantTVA!,
                            colorScheme.secondary,
                          ),
                          Divider(color: colorScheme.outlineVariant),
                          _buildResultRow(
                            context,
                            'Montant TTC:',
                            _montantTTC!,
                            colorScheme.tertiary,
                          ),
                        ],
                      ),
                    ),
                  ),
                  const SizedBox(height: 24),
                  const RASTVACheckerWidget(),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildResultRow(
    BuildContext context,
    String label,
    double montant,
    Color color,
  ) {
    final textTheme = Theme.of(context).textTheme;

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: textTheme.titleMedium?.copyWith(
              color: Theme.of(context).colorScheme.onSurface,
            ),
          ),
          Text(
            '${montant.toStringAsFixed(2)} DH',
            style: textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
        ],
      ),
    );
  }
}
