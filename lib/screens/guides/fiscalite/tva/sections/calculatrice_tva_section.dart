import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class CalculatriceTvaSection extends StatefulWidget {
  const CalculatriceTvaSection({super.key});

  @override
  State<CalculatriceTvaSection> createState() => _CalculatriceTvaSectionState();
}

class _CalculatriceTvaSectionState extends State<CalculatriceTvaSection> {
  final _formKey = GlobalKey<FormState>();
  final _montantController = TextEditingController();

  bool _isHT = true;
  double _selectedTaux = 20.0;
  String? _selectedCategorie;

  final Map<String, List<String>> _categoriesEtProduits = {
    'Produits alimentaires': [
      'Produits de large consommation',
      'Conserves de sardines',
      'Lait en poudre',
    ],
    'Services': [
      'Services bancaires',
      'Prestations d\'avocat',
      'Transport de voyageurs',
    ],
    'Industrie': [
      'Matières premières',
      'Machines industrielles',
      'Équipements professionnels',
    ],
  };

  double? _montantHT;
  double? _montantTVA;
  double? _montantTTC;

  void _calculerTVA() {
    if (_formKey.currentState!.validate()) {
      final montantSaisi =
          double.parse(_montantController.text.replaceAll(',', '.'));

      setState(() {
        if (_isHT) {
          _montantHT = montantSaisi;
          _montantTVA = _montantHT! * (_selectedTaux / 100);
          _montantTTC = _montantHT! + _montantTVA!;
        } else {
          _montantTTC = montantSaisi;
          _montantHT = _montantTTC! / (1 + (_selectedTaux / 100));
          _montantTVA = _montantTTC! - _montantHT!;
        }
      });
    }
  }

  @override
  void dispose() {
    _montantController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    colorScheme.primary,
                    colorScheme.primaryContainer,
                  ],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.circular(16),
                boxShadow: [
                  BoxShadow(
                    color: colorScheme.shadow.withOpacity(0.1),
                    blurRadius: 10,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Calculatrice TVA',
                    style: textTheme.headlineMedium?.copyWith(
                      color: colorScheme.onPrimary,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Calculez la TVA selon le type de produit ou service',
                    style: textTheme.titleMedium?.copyWith(
                      color: colorScheme.onPrimary.withOpacity(0.8),
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 24),
            Card(
              elevation: 2,
              surfaceTintColor: colorScheme.surfaceTint,
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Sélection du produit ou service',
                      style: textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: colorScheme.onSurface,
                      ),
                    ),
                    const SizedBox(height: 16),
                    DropdownButtonFormField<String>(
                      decoration: InputDecoration(
                        labelText: 'Catégorie',
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                        focusedBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                          borderSide: BorderSide(color: colorScheme.primary),
                        ),
                        enabledBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                          borderSide: BorderSide(color: colorScheme.outline),
                        ),
                        labelStyle: TextStyle(color: colorScheme.primary),
                      ),
                      value: _selectedCategorie,
                      items: _categoriesEtProduits.keys.map((categorie) {
                        return DropdownMenuItem(
                          value: categorie,
                          child: Text(categorie),
                        );
                      }).toList(),
                      onChanged: (value) {
                        setState(() {
                          _selectedCategorie = value;
                          if (value == 'Produits alimentaires') {
                            _selectedTaux = 10.0;
                          } else if (value == 'Services') {
                            _selectedTaux = 20.0;
                          }
                        });
                      },
                    ),
                    if (_selectedCategorie != null) ...[
                      const SizedBox(height: 16),
                      Text(
                        'Produits/Services disponibles :',
                        style: textTheme.bodyMedium?.copyWith(
                          color: colorScheme.onSurfaceVariant,
                        ),
                      ),
                      const SizedBox(height: 8),
                      ...(_categoriesEtProduits[_selectedCategorie]!).map(
                        (produit) => ListTile(
                          leading: Icon(
                            Icons.check_circle_outline,
                            color: colorScheme.primary,
                          ),
                          title: Text(
                            produit,
                            style: textTheme.bodyMedium?.copyWith(
                              color: colorScheme.onSurface,
                            ),
                          ),
                          dense: true,
                        ),
                      ),
                    ],
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),
            Card(
              elevation: 2,
              surfaceTintColor: colorScheme.surfaceTint,
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Calcul de la TVA',
                      style: textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: colorScheme.onSurface,
                      ),
                    ),
                    const SizedBox(height: 16),
                    Row(
                      children: [
                        Expanded(
                          child: TextFormField(
                            controller: _montantController,
                            decoration: InputDecoration(
                              labelText: 'Montant ${_isHT ? 'HT' : 'TTC'}',
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(8),
                              ),
                              focusedBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(8),
                                borderSide:
                                    BorderSide(color: colorScheme.primary),
                              ),
                              enabledBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(8),
                                borderSide:
                                    BorderSide(color: colorScheme.outline),
                              ),
                              prefixText: 'MAD ',
                              labelStyle: TextStyle(color: colorScheme.primary),
                            ),
                            keyboardType: const TextInputType.numberWithOptions(
                              decimal: true,
                            ),
                            inputFormatters: [
                              FilteringTextInputFormatter.allow(
                                RegExp(r'^\d*[,.]?\d*'),
                              ),
                            ],
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return 'Veuillez saisir un montant';
                              }
                              if (double.tryParse(value.replaceAll(',', '.')) ==
                                  null) {
                                return 'Montant invalide';
                              }
                              return null;
                            },
                          ),
                        ),
                        const SizedBox(width: 16),
                        ToggleButtons(
                          direction: Axis.vertical,
                          isSelected: [_isHT, !_isHT],
                          onPressed: (index) {
                            setState(() {
                              _isHT = index == 0;
                            });
                          },
                          selectedColor: colorScheme.onPrimaryContainer,
                          color: colorScheme.onSurface,
                          fillColor: colorScheme.primaryContainer,
                          borderRadius: BorderRadius.circular(8),
                          constraints: const BoxConstraints(
                            minWidth: 60,
                            minHeight: 36,
                          ),
                          children: const [
                            Padding(
                              padding: EdgeInsets.all(8.0),
                              child: Text('HT'),
                            ),
                            Padding(
                              padding: EdgeInsets.all(8.0),
                              child: Text('TTC'),
                            ),
                          ],
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
                    Row(
                      children: [
                        Expanded(
                          child: DropdownButtonFormField<double>(
                            decoration: InputDecoration(
                              labelText: 'Taux de TVA',
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(8),
                              ),
                              focusedBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(8),
                                borderSide:
                                    BorderSide(color: colorScheme.primary),
                              ),
                              enabledBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(8),
                                borderSide:
                                    BorderSide(color: colorScheme.outline),
                              ),
                              labelStyle: TextStyle(color: colorScheme.primary),
                            ),
                            value: _selectedTaux,
                            items: const [
                              DropdownMenuItem(
                                value: 20.0,
                                child: Text('20% - Taux normal'),
                              ),
                              DropdownMenuItem(
                                value: 10.0,
                                child: Text('10% - Taux intermédiaire'),
                              ),
                              DropdownMenuItem(
                                value: 0.0,
                                child: Text('0% - Exonéré'),
                              ),
                            ],
                            onChanged: (value) {
                              setState(() {
                                _selectedTaux = value!;
                              });
                            },
                          ),
                        ),
                        const SizedBox(width: 16),
                        IconButton.filledTonal(
                          onPressed: () {
                            showDialog(
                              context: context,
                              builder: (context) => AlertDialog(
                                title: Text(
                                  'Aide sur les taux de TVA',
                                  style: textTheme.titleLarge?.copyWith(
                                    color: colorScheme.onSurface,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                content: SingleChildScrollView(
                                  child: Text(
                                    '• 20% : Taux normal applicable à la majorité des biens et services\n\n'
                                    '• 10% : Taux intermédiaire pour certains produits et services (alimentation, transport...)\n\n'
                                    '• 0% : Exonération avec droit à déduction',
                                    style: textTheme.bodyMedium?.copyWith(
                                      color: colorScheme.onSurfaceVariant,
                                    ),
                                  ),
                                ),
                                actions: [
                                  TextButton(
                                    onPressed: () => Navigator.pop(context),
                                    child: Text(
                                      'Fermer',
                                      style: textTheme.labelLarge?.copyWith(
                                        color: colorScheme.primary,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            );
                          },
                          icon: const Icon(Icons.help_outline),
                        ),
                      ],
                    ),
                    const SizedBox(height: 24),
                    SizedBox(
                      width: double.infinity,
                      child: FilledButton.icon(
                        onPressed: _calculerTVA,
                        icon: const Icon(Icons.calculate),
                        label: const Text('Calculer'),
                      ),
                    ),
                  ],
                ),
              ),
            ),
            if (_montantHT != null &&
                _montantTVA != null &&
                _montantTTC != null) ...[
              const SizedBox(height: 16),
              Card(
                elevation: 2,
                surfaceTintColor: colorScheme.surfaceTint,
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Résultats',
                        style: textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: colorScheme.onSurface,
                        ),
                      ),
                      const SizedBox(height: 16),
                      _buildResultRow(
                        context,
                        label: 'Montant HT',
                        value: _montantHT!,
                        icon: Icons.money_off,
                      ),
                      Divider(color: colorScheme.outlineVariant),
                      _buildResultRow(
                        context,
                        label: 'Montant TVA',
                        value: _montantTVA!,
                        icon: Icons.percent,
                      ),
                      Divider(color: colorScheme.outlineVariant),
                      _buildResultRow(
                        context,
                        label: 'Montant TTC',
                        value: _montantTTC!,
                        icon: Icons.money,
                        isTotal: true,
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildResultRow(
    BuildContext context, {
    required String label,
    required double value,
    required IconData icon,
    bool isTotal = false,
  }) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;
    final style = isTotal
        ? textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
            color: colorScheme.onSurface,
          )
        : textTheme.bodyMedium?.copyWith(
            color: colorScheme.onSurfaceVariant,
          );

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Icon(
            icon,
            color: colorScheme.primary,
            size: 20,
          ),
          const SizedBox(width: 8),
          Text(label, style: style),
          const Spacer(),
          Text(
            '${value.toStringAsFixed(2)} MAD',
            style: style,
          ),
        ],
      ),
    );
  }
}
