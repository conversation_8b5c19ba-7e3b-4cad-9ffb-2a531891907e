import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class IRCalculatorSection extends StatefulWidget {
  const IRCalculatorSection({super.key});

  @override
  State<IRCalculatorSection> createState() => _IRCalculatorSectionState();
}

class _IRCalculatorSectionState extends State<IRCalculatorSection> {
  final _formKey = GlobalKey<FormState>();
  final _salaryController = TextEditingController();
  bool _isMarried = false;
  int _numberOfChildren = 0;
  bool _hasCNSS = true;
  bool _hasAMO = true;
  double _seniorityYears = 0;

  @override
  void dispose() {
    _salaryController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Calculatrice IR',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    color: colorScheme.primary,
                    fontWeight: FontWeight.bold,
                  ),
            ),
            const SizedBox(height: 24),

            // Salaire brut
            TextFormField(
              controller: _salaryController,
              decoration: const InputDecoration(
                labelText: 'Salaire Brut Mensuel',
                suffixText: 'DH',
                border: OutlineInputBorder(),
              ),
              keyboardType: TextInputType.number,
              inputFormatters: [FilteringTextInputFormatter.digitsOnly],
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Veuillez saisir le salaire';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),

            // Situation familiale
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Situation Familiale',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    const SizedBox(height: 8),
                    SwitchListTile(
                      title: const Text('Marié(e)'),
                      value: _isMarried,
                      onChanged: (bool value) {
                        setState(() {
                          _isMarried = value;
                        });
                      },
                    ),
                    if (_isMarried) ...[
                      const SizedBox(height: 8),
                      Row(
                        children: [
                          const Text('Nombre d\'enfants:'),
                          const SizedBox(width: 16),
                          SegmentedButton<int>(
                            segments: const [
                              ButtonSegment(value: 0, label: Text('0')),
                              ButtonSegment(value: 1, label: Text('1')),
                              ButtonSegment(value: 2, label: Text('2')),
                              ButtonSegment(value: 3, label: Text('3')),
                              ButtonSegment(value: 4, label: Text('4')),
                              ButtonSegment(value: 5, label: Text('5')),
                              ButtonSegment(value: 6, label: Text('6+')),
                            ],
                            selected: {_numberOfChildren},
                            onSelectionChanged: (Set<int> newSelection) {
                              setState(() {
                                _numberOfChildren = newSelection.first;
                              });
                            },
                          ),
                        ],
                      ),
                    ],
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),

            // Cotisations sociales
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Cotisations Sociales',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    const SizedBox(height: 8),
                    SwitchListTile(
                      title: const Text('CNSS'),
                      subtitle: const Text('Cotisation sociale'),
                      value: _hasCNSS,
                      onChanged: (bool value) {
                        setState(() {
                          _hasCNSS = value;
                        });
                      },
                    ),
                    SwitchListTile(
                      title: const Text('AMO'),
                      subtitle: const Text('Assurance Maladie Obligatoire'),
                      value: _hasAMO,
                      onChanged: (bool value) {
                        setState(() {
                          _hasAMO = value;
                        });
                      },
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),

            // Ancienneté
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Ancienneté',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    const SizedBox(height: 16),
                    Slider(
                      value: _seniorityYears,
                      min: 0,
                      max: 30,
                      divisions: 30,
                      label: '${_seniorityYears.round()} ans',
                      onChanged: (double value) {
                        setState(() {
                          _seniorityYears = value;
                        });
                      },
                    ),
                    Center(
                      child: Text(
                        '${_seniorityYears.round()} ans d\'ancienneté',
                        style: Theme.of(context).textTheme.bodyMedium,
                      ),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 24),

            // Bouton de calcul
            SizedBox(
              width: double.infinity,
              child: FilledButton.icon(
                onPressed: () {
                  if (_formKey.currentState!.validate()) {
                    // TODO: Implement IR calculation
                  }
                },
                icon: const Icon(Icons.calculate),
                label: const Text('Calculer l\'IR'),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
