import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../../../../models/salary/salary_data.dart';
import '../../../../../models/salary/salary_result.dart';
import '../../../../../services/salary_calculator_service.dart';

class CalculatorSection extends StatefulWidget {
  const CalculatorSection({super.key});

  @override
  State<CalculatorSection> createState() => _CalculatorSectionState();
}

class _CalculatorSectionState extends State<CalculatorSection> {
  // Contrôleurs pour les champs de texte
  final _baseSalaryController = TextEditingController();
  final _transportController = TextEditingController();
  final _housingController = TextEditingController();
  final _otherAllowancesController = TextEditingController();
  final _yearsOfServiceController = TextEditingController();

  // Type d'employé
  String _employeeType = 'Salarié'; // ou 'Cadre'

  // Options
  bool _includeCNSS = true;

  // Heures supplémentaires
  final _regularOvertimeController = TextEditingController();
  final _holidayOvertimeController = TextEditingController();
  final _nightOvertimeController = TextEditingController();
  bool _showOvertimeSection = false;

  // Primes
  final Map<String, (TextEditingController, bool, bool)> _bonuses = {
    'Prime de rendement': (TextEditingController(), false, false),
    '13ème mois': (TextEditingController(), false, true),
    'Prime de bilan': (TextEditingController(), false, true),
  };

  // Situation familiale
  bool _isMarried = false;
  final List<(TextEditingController, bool)> _dependents = [];

  // Résultat
  SalaryResult? _result;
  final _calculatorService = SalaryCalculatorService();

  @override
  void dispose() {
    _baseSalaryController.dispose();
    _transportController.dispose();
    _housingController.dispose();
    _otherAllowancesController.dispose();
    _yearsOfServiceController.dispose();
    _regularOvertimeController.dispose();
    _holidayOvertimeController.dispose();
    _nightOvertimeController.dispose();

    for (final bonus in _bonuses.values) {
      bonus.$1.dispose();
    }

    for (final dependent in _dependents) {
      dependent.$1.dispose();
    }

    super.dispose();
  }

  Future<void> _calculate() async {
    if (_baseSalaryController.text.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Veuillez saisir le salaire de base'),
        ),
      );
      return;
    }

    // 1. Construire les données
    final data = SalaryData(
      baseSalary: double.tryParse(_baseSalaryController.text) ?? 0,
      transportAllowance: double.tryParse(_transportController.text) ?? 0,
      housingAllowance: double.tryParse(_housingController.text) ?? 0,
      otherAllowances: double.tryParse(_otherAllowancesController.text) ?? 0,
      employeeType: _employeeType,
      yearsOfService: int.tryParse(_yearsOfServiceController.text) ?? 0,
      includeCNSS: _includeCNSS,
      overtime: OvertimeHours(
        regularHours: double.tryParse(_regularOvertimeController.text) ?? 0,
        holidayHours: double.tryParse(_holidayOvertimeController.text) ?? 0,
        nightHours: double.tryParse(_nightOvertimeController.text) ?? 0,
      ),
      bonuses: Map.fromEntries(
        _bonuses.entries.map((e) => MapEntry(
              e.key,
              BonusEntry(
                enabled: e.value.$2,
                amount: double.tryParse(e.value.$1.text) ?? 0,
                isAnnual: e.value.$3,
              ),
            )),
      ),
      familyStatus: FamilyStatus(
        isMarried: _isMarried,
        dependents: _dependents
            .map((e) => Dependent(
                  age: int.tryParse(e.$1.text) ?? 0,
                  isDisabled: e.$2,
                ))
            .toList(),
      ),
    );

    // 2. Calculer
    try {
      final result = await _calculatorService.calculate(data);
      setState(() {
        _result = result;
      });
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Erreur: ${e.toString()}'),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  Theme.of(context).colorScheme.primary,
                  Theme.of(context).colorScheme.primaryContainer,
                ],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: Theme.of(context).colorScheme.shadow.withOpacity(0.1),
                  blurRadius: 10,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Calculateur IR',
                  style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                        color: Theme.of(context).colorScheme.onPrimary,
                        fontWeight: FontWeight.bold,
                      ),
                ),
                const SizedBox(height: 8),
                Text(
                  'Calculez votre impôt sur le revenu en quelques clics',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        color: Theme.of(context)
                            .colorScheme
                            .onPrimary
                            .withOpacity(0.8),
                      ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 24),
          // 1. Informations de base
          _buildBasicSection(),
          const SizedBox(height: 24),

          // 2. Indemnités fixes
          _buildAllowancesSection(),
          const SizedBox(height: 24),

          // 3. Heures supplémentaires
          _buildOvertimeSection(),
          const SizedBox(height: 24),

          // 4. Primes
          _buildBonusesSection(),
          const SizedBox(height: 24),

          // 5. Situation familiale
          _buildFamilySection(),
          const SizedBox(height: 24),

          // 6. Bouton de calcul
          FilledButton.icon(
            onPressed: _calculate,
            icon: const Icon(Icons.calculate),
            label: Text(
              'Calculer',
              style: Theme.of(context).textTheme.labelLarge?.copyWith(
                    color: Theme.of(context).colorScheme.onPrimary,
                  ),
            ),
            style: FilledButton.styleFrom(
              backgroundColor: Theme.of(context).colorScheme.primary,
              padding: const EdgeInsets.symmetric(
                horizontal: 24,
                vertical: 16,
              ),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          ),
          const SizedBox(height: 24),

          // 7. Résultat
          if (_result != null) _buildResultSection(),
        ],
      ),
    );
  }

  Widget _buildBasicSection() {
    return Card(
      elevation: 2,
      surfaceTintColor: Theme.of(context).colorScheme.surfaceTint,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.person_outline,
                  color: Theme.of(context).colorScheme.primary,
                  size: 24,
                ),
                const SizedBox(width: 12),
                Text(
                  'Informations de base',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        color: Theme.of(context).colorScheme.primary,
                        fontWeight: FontWeight.bold,
                      ),
                ),
              ],
            ),
            const SizedBox(height: 20),
            // Type d'employé
            SegmentedButton<String>(
              segments: const [
                ButtonSegment(
                  value: 'Salarié',
                  label: Text('Salarié'),
                  icon: Icon(Icons.work_outline),
                ),
                ButtonSegment(
                  value: 'Cadre',
                  label: Text('Cadre'),
                  icon: Icon(Icons.business_center_outlined),
                ),
              ],
              selected: {_employeeType},
              onSelectionChanged: (Set<String> newSelection) {
                setState(() {
                  _employeeType = newSelection.first;
                });
              },
              style: ButtonStyle(
                backgroundColor: WidgetStateProperty.resolveWith<Color>(
                  (Set<WidgetState> states) {
                    if (states.contains(WidgetState.selected)) {
                      return Theme.of(context).colorScheme.primaryContainer;
                    }
                    return Theme.of(context).colorScheme.surface;
                  },
                ),
              ),
            ),
            const SizedBox(height: 20),
            // Salaire de base
            TextFormField(
              controller: _baseSalaryController,
              decoration: InputDecoration(
                labelText: 'Salaire de base',
                suffixText: 'DH',
                prefixIcon: Icon(
                  Icons.payments_outlined,
                  color: Theme.of(context).colorScheme.primary,
                ),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              keyboardType: TextInputType.number,
              inputFormatters: [
                FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*')),
              ],
            ),
            const SizedBox(height: 16),
            // Ancienneté
            TextFormField(
              controller: _yearsOfServiceController,
              decoration: InputDecoration(
                labelText: 'Ancienneté',
                suffixText: 'années',
                prefixIcon: Icon(
                  Icons.timer_outlined,
                  color: Theme.of(context).colorScheme.primary,
                ),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              keyboardType: TextInputType.number,
              inputFormatters: [
                FilteringTextInputFormatter.digitsOnly,
              ],
            ),
            const SizedBox(height: 16),
            // CNSS
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Theme.of(context)
                    .colorScheme
                    .surfaceContainerHighest
                    .withOpacity(0.3),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: Theme.of(context).colorScheme.outlineVariant,
                ),
              ),
              child: SwitchListTile(
                title: Text(
                  'Inclure CNSS et AMO',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        color: Theme.of(context).colorScheme.primary,
                        fontWeight: FontWeight.bold,
                      ),
                ),
                subtitle: Text(
                  'Cotisations sociales',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Theme.of(context).colorScheme.onSurfaceVariant,
                      ),
                ),
                value: _includeCNSS,
                onChanged: (bool value) {
                  setState(() {
                    _includeCNSS = value;
                  });
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAllowancesSection() {
    return Card(
      elevation: 2,
      surfaceTintColor: Theme.of(context).colorScheme.surfaceTint,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.card_giftcard_outlined,
                  color: Theme.of(context).colorScheme.primary,
                  size: 24,
                ),
                const SizedBox(width: 12),
                Text(
                  'Indemnités fixes',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        color: Theme.of(context).colorScheme.primary,
                        fontWeight: FontWeight.bold,
                      ),
                ),
              ],
            ),
            const SizedBox(height: 20),
            TextFormField(
              controller: _transportController,
              decoration: InputDecoration(
                labelText: 'Indemnité de transport',
                suffixText: 'DH',
                prefixIcon: Icon(
                  Icons.directions_car_outlined,
                  color: Theme.of(context).colorScheme.primary,
                ),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              keyboardType: TextInputType.number,
              inputFormatters: [
                FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*')),
              ],
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _housingController,
              decoration: InputDecoration(
                labelText: 'Indemnité de logement',
                suffixText: 'DH',
                prefixIcon: Icon(
                  Icons.home_outlined,
                  color: Theme.of(context).colorScheme.primary,
                ),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              keyboardType: TextInputType.number,
              inputFormatters: [
                FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*')),
              ],
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _otherAllowancesController,
              decoration: InputDecoration(
                labelText: 'Autres indemnités',
                suffixText: 'DH',
                prefixIcon: Icon(
                  Icons.add_circle_outline,
                  color: Theme.of(context).colorScheme.primary,
                ),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              keyboardType: TextInputType.number,
              inputFormatters: [
                FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*')),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildOvertimeSection() {
    return Card(
      elevation: 2,
      surfaceTintColor: Theme.of(context).colorScheme.surfaceTint,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.timer_outlined,
                  color: Theme.of(context).colorScheme.primary,
                  size: 24,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    'Heures supplémentaires',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          color: Theme.of(context).colorScheme.primary,
                          fontWeight: FontWeight.bold,
                        ),
                  ),
                ),
                IconButton(
                  icon: Icon(
                    _showOvertimeSection
                        ? Icons.expand_less
                        : Icons.expand_more,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                  onPressed: () {
                    setState(() {
                      _showOvertimeSection = !_showOvertimeSection;
                    });
                  },
                ),
              ],
            ),
            if (_showOvertimeSection) ...[
              const SizedBox(height: 20),
              TextFormField(
                controller: _regularOvertimeController,
                decoration: InputDecoration(
                  labelText: 'Heures normales (25%)',
                  suffixText: 'heures',
                  prefixIcon: Icon(
                    Icons.access_time_outlined,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                keyboardType: TextInputType.number,
                inputFormatters: [
                  FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*')),
                ],
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _holidayOvertimeController,
                decoration: InputDecoration(
                  labelText: 'Heures jours fériés (100%)',
                  suffixText: 'heures',
                  prefixIcon: Icon(
                    Icons.event_outlined,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                keyboardType: TextInputType.number,
                inputFormatters: [
                  FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*')),
                ],
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _nightOvertimeController,
                decoration: InputDecoration(
                  labelText: 'Heures de nuit (50%)',
                  suffixText: 'heures',
                  prefixIcon: Icon(
                    Icons.nightlight_outlined,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                keyboardType: TextInputType.number,
                inputFormatters: [
                  FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*')),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildBonusesSection() {
    return Card(
      elevation: 2,
      surfaceTintColor: Theme.of(context).colorScheme.surfaceTint,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.star_outline,
                  color: Theme.of(context).colorScheme.primary,
                  size: 24,
                ),
                const SizedBox(width: 12),
                Text(
                  'Primes',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        color: Theme.of(context).colorScheme.primary,
                        fontWeight: FontWeight.bold,
                      ),
                ),
              ],
            ),
            const SizedBox(height: 20),
            ..._bonuses.entries.map((entry) {
              final (controller, enabled, isAnnual) = entry.value;
              return Container(
                margin: const EdgeInsets.only(bottom: 16),
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Theme.of(context)
                      .colorScheme
                      .surfaceContainerHighest
                      .withOpacity(0.3),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: Theme.of(context).colorScheme.outlineVariant,
                  ),
                ),
                child: Row(
                  children: [
                    Checkbox(
                      value: enabled,
                      onChanged: (value) {
                        setState(() {
                          _bonuses[entry.key] =
                              (controller, value ?? false, isAnnual);
                        });
                      },
                    ),
                    Expanded(
                      child: TextFormField(
                        controller: controller,
                        enabled: enabled,
                        decoration: InputDecoration(
                          labelText:
                              '${entry.key}${isAnnual ? ' (annuel)' : ''}',
                          suffixText: 'DH',
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                        keyboardType: TextInputType.number,
                        inputFormatters: [
                          FilteringTextInputFormatter.allow(
                              RegExp(r'^\d*\.?\d*')),
                        ],
                      ),
                    ),
                  ],
                ),
              );
            }),
          ],
        ),
      ),
    );
  }

  Widget _buildFamilySection() {
    return Card(
      elevation: 2,
      surfaceTintColor: Theme.of(context).colorScheme.surfaceTint,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.family_restroom,
                  color: Theme.of(context).colorScheme.primary,
                  size: 24,
                ),
                const SizedBox(width: 12),
                Text(
                  'Situation familiale',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        color: Theme.of(context).colorScheme.primary,
                        fontWeight: FontWeight.bold,
                      ),
                ),
              ],
            ),
            const SizedBox(height: 20),
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Theme.of(context)
                    .colorScheme
                    .surfaceContainerHighest
                    .withOpacity(0.3),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: Theme.of(context).colorScheme.outlineVariant,
                ),
              ),
              child: SwitchListTile(
                title: Text(
                  'Marié(e)',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        color: Theme.of(context).colorScheme.primary,
                        fontWeight: FontWeight.bold,
                      ),
                ),
                value: _isMarried,
                onChanged: (value) {
                  setState(() {
                    _isMarried = value;
                  });
                },
              ),
            ),
            const SizedBox(height: 20),
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Enfants à charge',
                        style:
                            Theme.of(context).textTheme.titleMedium?.copyWith(
                                  color: Theme.of(context).colorScheme.primary,
                                  fontWeight: FontWeight.bold,
                                ),
                      ),
                      const SizedBox(height: 4),
                      Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: Theme.of(context)
                              .colorScheme
                              .tertiaryContainer
                              .withOpacity(0.3),
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(
                            color: Theme.of(context)
                                .colorScheme
                                .tertiary
                                .withOpacity(0.2),
                          ),
                        ),
                        child: Row(
                          children: [
                            Icon(
                              Icons.info_outline,
                              size: 16,
                              color: Theme.of(context).colorScheme.tertiary,
                            ),
                            const SizedBox(width: 8),
                            Expanded(
                              child: Text(
                                'Conditions: âge < 27 ans et revenu < seuil exonéré',
                                style: Theme.of(context)
                                    .textTheme
                                    .bodySmall
                                    ?.copyWith(
                                      color: Theme.of(context)
                                          .colorScheme
                                          .tertiary,
                                    ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
                IconButton(
                  icon: Icon(
                    Icons.add_circle_outline,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                  onPressed: () {
                    setState(() {
                      _dependents.add((TextEditingController(), false));
                    });
                  },
                ),
              ],
            ),
            const SizedBox(height: 16),
            ..._dependents.asMap().entries.map((entry) {
              final index = entry.key;
              final (controller, isDisabled) = entry.value;
              return Container(
                margin: const EdgeInsets.only(bottom: 16),
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Theme.of(context)
                      .colorScheme
                      .surfaceContainerHighest
                      .withOpacity(0.3),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: Theme.of(context).colorScheme.outlineVariant,
                  ),
                ),
                child: Row(
                  children: [
                    Expanded(
                      child: TextFormField(
                        controller: controller,
                        decoration: InputDecoration(
                          labelText: 'Âge enfant ${index + 1}',
                          errorText: () {
                            if (controller.text.isEmpty) return null;
                            final age = int.tryParse(controller.text);
                            if (age == null) return 'Âge invalide';
                            if (!isDisabled && age >= 27) {
                              return 'L\'âge doit être inférieur à 27 ans sauf en cas d\'infirmité';
                            }
                            return null;
                          }(),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                        keyboardType: TextInputType.number,
                        inputFormatters: [
                          FilteringTextInputFormatter.digitsOnly,
                        ],
                        onChanged: (value) {
                          setState(() {});
                        },
                      ),
                    ),
                    const SizedBox(width: 16),
                    Column(
                      children: [
                        Checkbox(
                          value: isDisabled,
                          onChanged: (value) {
                            setState(() {
                              _dependents[index] = (controller, value ?? false);
                            });
                          },
                        ),
                        Text(
                          'Atteint\nd\'infirmité',
                          style:
                              Theme.of(context).textTheme.bodySmall?.copyWith(
                                    color: Theme.of(context)
                                        .colorScheme
                                        .onSurfaceVariant,
                                  ),
                          textAlign: TextAlign.center,
                        ),
                      ],
                    ),
                    IconButton(
                      icon: Icon(
                        Icons.remove_circle_outline,
                        color: Theme.of(context).colorScheme.error,
                      ),
                      onPressed: () {
                        setState(() {
                          controller.dispose();
                          _dependents.removeAt(index);
                        });
                      },
                    ),
                  ],
                ),
              );
            }),
          ],
        ),
      ),
    );
  }

  Widget _buildResultSection() {
    return Card(
      elevation: 2,
      surfaceTintColor: Theme.of(context).colorScheme.surfaceTint,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.receipt_long_outlined,
                  color: Theme.of(context).colorScheme.primary,
                  size: 24,
                ),
                const SizedBox(width: 12),
                Text(
                  'Résultat du calcul',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        color: Theme.of(context).colorScheme.primary,
                        fontWeight: FontWeight.bold,
                      ),
                ),
              ],
            ),
            const SizedBox(height: 20),
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Theme.of(context)
                    .colorScheme
                    .surfaceContainerHighest
                    .withOpacity(0.3),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: Theme.of(context).colorScheme.outlineVariant,
                ),
              ),
              child: SingleChildScrollView(
                scrollDirection: Axis.horizontal,
                child: SelectableText(
                  _result!.generateDetails(),
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        fontFamily: 'monospace',
                        color: Theme.of(context).colorScheme.onSurface,
                      ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
