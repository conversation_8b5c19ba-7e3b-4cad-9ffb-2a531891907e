import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:intl/intl.dart';
import '../../../../../services/is_calculator_service.dart';

class IsCalculatorSection extends StatefulWidget {
  const IsCalculatorSection({super.key});

  @override
  State<IsCalculatorSection> createState() => _IsCalculatorSectionState();
}

class _IsCalculatorSectionState extends State<IsCalculatorSection> {
  final _formKey = GlobalKey<FormState>();
  final _numberFormat = NumberFormat("#,##0.00", "fr_FR");

  // Controllers for input fields
  final _accountingResultController = TextEditingController();
  final _regularRevenueController = TextEditingController();
  final _marginProductsRevenueController = TextEditingController();
  final _petroleumProductsRevenueController = TextEditingController();

  // State variables for calculation results
  double _taxableBenefit = 0.0;
  double _isAmount = 0.0;
  double _cmAmount = 0.0;
  double _finalAmount = 0.0;
  double _totalReintegrations = 0.0;
  double _totalDeductions = 0.0;

  // State for JSON data
  Map<String, dynamic>? _tauxData;
  Map<String, dynamic>? _reintegrationData;
  bool _isLoading = true;

  // State for CM options
  final bool _isFirstYear = false;
  final bool _isExempted = false;

  // Map to store controllers for dynamic items
  Map<String, TextEditingController>? _itemControllers;

  // State variables for sector selection
  String? _selectedSector;

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  Future<void> _loadData() async {
    try {
      _tauxData = await IsCalculatorService.loadIsTaux();
      _reintegrationData = await IsCalculatorService.loadIsReintegrations();

      // Initialize selected sector with the first regime from the data
      final regimes = _tauxData?['regimes'] as List<dynamic>? ?? [];
      if (regimes.isNotEmpty) {
        _selectedSector = regimes.first['name'] as String?;
      }

      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      print('Error loading data: $e');
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _calculateTotals() {
    // Calculate totals from input fields
    double accountingResult = double.tryParse(_accountingResultController.text
            .replaceAll(RegExp(r'[^0-9\.]'), '')) ??
        0.0;

    // Calculate reintegrations
    _totalReintegrations = [
      // Charges non déductibles
      'amendes',
      'impot_societe',
      'penalites',
      // Charges sur exercice antérieur
      'charges_anterieures',
      // Charges non justifiées
      'achats_non_justifies',
      'services_non_justifies',
      // Amortissements
      'amort_vehicules',
      'amort_taux_eleve',
      // Provisions
      'prov_clients',
      'prov_stocks',
      'prov_risques',
      // Charges financières
      'interets_non_deductibles',
      'ecarts_conversion',
      // Autres réintégrations
      'dons',
      'cadeaux',
      'charges_personnel',
    ]
        .map((id) =>
            double.tryParse(_getControllerForItem(id)
                .text
                .replaceAll(RegExp(r'[^0-9\.]'), '')) ??
            0.0)
        .fold(0.0, (a, b) => a + b);

    // Calculate deductions
    _totalDeductions = [
      // Produits non imposables
      'dividendes',
      'plus_values',
      'revenus_source_etrangere',
      // Reprises et provisions
      'reprises_provisions',
      'reprises_amortissements',
      // Autres déductions
      'abattement_export',
      'autres_deductions',
    ]
        .map((id) =>
            double.tryParse(_getControllerForItem(id)
                .text
                .replaceAll(RegExp(r'[^0-9\.]'), '')) ??
            0.0)
        .fold(0.0, (a, b) => a + b);

    setState(() {
      // Calculate taxable benefit
      _taxableBenefit =
          accountingResult + _totalReintegrations - _totalDeductions;

      // Calculate IS based on taxable benefit and selected sector
      _isAmount = IsCalculatorService.calculateIS(
        _taxableBenefit,
        _selectedSector ?? 'common', // Provide default value for null case
        _tauxData ?? {},
      );

      // Calculate CM
      final totalRevenue = double.tryParse(_regularRevenueController.text
              .replaceAll(RegExp(r'[^0-9\.]'), '')) ??
          0;
      _cmAmount = IsCalculatorService.calculateCM(
        totalRevenue,
        'normal',
        _tauxData ?? {},
        _isExempted,
        _isFirstYear,
      );

      // Final amount is the greater of IS and CM
      _finalAmount = _isAmount > _cmAmount ? _isAmount : _cmAmount;
    });
  }

  Map<String, dynamic> _getReintegrationValues() {
    if (_reintegrationData == null || _itemControllers == null) return {};

    final values = <String, dynamic>{};
    final reintegrations =
        _reintegrationData!['reintegrations'] as List<dynamic>;

    for (final category in reintegrations) {
      final items = category['items'] as List<dynamic>;
      for (final item in items) {
        final id = item['id'] as String;
        if (_itemControllers!.containsKey(id)) {
          final value = double.tryParse(_itemControllers![id]!.text) ?? 0.0;
          if (value > 0) {
            values[id] = value;
          }
        }
      }
    }

    return values;
  }

  Map<String, dynamic> _getDeductionValues() {
    if (_reintegrationData == null || _itemControllers == null) return {};

    final values = <String, dynamic>{};
    final deductions = _reintegrationData!['deductions'] as List<dynamic>;

    for (final category in deductions) {
      final items = category['items'] as List<dynamic>;
      for (final item in items) {
        final id = item['id'] as String;
        if (_itemControllers!.containsKey(id)) {
          final value = double.tryParse(_itemControllers![id]!.text) ?? 0.0;
          if (value > 0) {
            values[id] = value;
          }
        }
      }
    }

    return values;
  }

  // Helper method to build reintegration fields
  Widget _buildReintegrationFields(Map<String, dynamic> category) {
    final items = category['items'] as List<dynamic>? ?? [];
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: items.map<Widget>((item) {
        final title = item['title'] as String? ?? 'Sans titre';
        final description = item['description'] as String? ?? '';
        final condition = item['condition'] as String?;
        final id =
            item['id'] as String? ?? title.toLowerCase().replaceAll(' ', '_');

        return Tooltip(
          message: condition != null
              ? '$description\nCondition: $condition'
              : description,
          child: _buildNumberInput(
            _getControllerForItem(id),
            title,
            hint: description,
          ),
        );
      }).toList(),
    );
  }

  // Helper method to get or create controller for an item
  TextEditingController _getControllerForItem(String id) {
    // Create a map to store controllers if it doesn't exist
    _itemControllers ??= <String, TextEditingController>{};

    // Return existing controller or create a new one
    return _itemControllers!.putIfAbsent(
      id,
      () => TextEditingController(),
    );
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    if (_isLoading) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(
              color: colorScheme.primary,
            ),
            const SizedBox(height: 16),
            Text(
              'Chargement des données...',
              style: textTheme.titleMedium?.copyWith(
                color: colorScheme.onSurface,
              ),
            ),
          ],
        ),
      );
    }

    final regimes = _tauxData?['regimes'] as List<dynamic>? ?? [];
    if (regimes.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 48,
              color: colorScheme.error,
            ),
            const SizedBox(height: 16),
            Text(
              'Aucun secteur d\'activité disponible',
              style: textTheme.titleLarge?.copyWith(
                color: colorScheme.onSurface,
              ),
            ),
            const SizedBox(height: 8),
            FilledButton.icon(
              onPressed: _loadData,
              icon: const Icon(Icons.refresh),
              label: const Text('Réessayer'),
              style: FilledButton.styleFrom(
                backgroundColor: colorScheme.primary,
                foregroundColor: colorScheme.onPrimary,
              ),
            ),
          ],
        ),
      );
    }

    // Ensure _selectedSector has a valid value
    if (_selectedSector == null ||
        !regimes.any((r) => r['name'] == _selectedSector)) {
      _selectedSector = regimes.first['name'] as String?;
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // En-tête avec description
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    colorScheme.primary,
                    colorScheme.primaryContainer,
                  ],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.circular(16),
                boxShadow: [
                  BoxShadow(
                    color: colorScheme.shadow.withOpacity(0.1),
                    blurRadius: 10,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: colorScheme.onPrimary.withOpacity(0.2),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Icon(
                          Icons.calculate,
                          color: colorScheme.onPrimary,
                          size: 28,
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Calculateur d\'IS',
                              style: textTheme.headlineMedium?.copyWith(
                                color: colorScheme.onPrimary,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(height: 8),
                            Text(
                              'Calculez votre Impôt sur les Sociétés en quelques étapes',
                              style: textTheme.titleMedium?.copyWith(
                                color: colorScheme.onPrimary.withOpacity(0.9),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            const SizedBox(height: 24),

            // 0. Secteur d'Activité
            _buildSectionCard(
              'Secteur d\'Activité',
              [
                Text(
                  'Sélectionnez votre secteur d\'activité pour appliquer le taux d\'IS correspondant.',
                  style: textTheme.bodyMedium?.copyWith(
                    color: colorScheme.onSurfaceVariant,
                    fontStyle: FontStyle.italic,
                  ),
                ),
                const SizedBox(height: 16),
                DropdownButtonFormField<String>(
                  value: _selectedSector,
                  isExpanded: true,
                  decoration: InputDecoration(
                    labelText: 'Secteur d\'activité',
                    filled: true,
                    fillColor: colorScheme.surfaceContainerHighest.withOpacity(0.3),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide.none,
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide(
                        color: colorScheme.outline.withOpacity(0.3),
                      ),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide(
                        color: colorScheme.primary,
                        width: 2,
                      ),
                    ),
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 12,
                    ),
                  ),
                  items: regimes.map<DropdownMenuItem<String>>((regime) {
                    final name = regime['name'] as String? ?? '';
                    final description = regime['description'] as String? ?? '';
                    return DropdownMenuItem(
                      value: name,
                      child: Text(
                        description,
                        overflow: TextOverflow.ellipsis,
                        style: textTheme.bodyMedium?.copyWith(
                          color: colorScheme.onSurface,
                        ),
                      ),
                    );
                  }).toList(),
                  onChanged: (String? value) {
                    if (value != null) {
                      setState(() {
                        _selectedSector = value;
                        _calculateTotals();
                      });
                    }
                  },
                ),
                if (_selectedSector != null)
                  Padding(
                    padding: const EdgeInsets.only(top: 8.0),
                    child: Text(
                      _getSectorDescription(),
                      style: textTheme.bodySmall?.copyWith(
                        color: colorScheme.primary,
                        fontStyle: FontStyle.italic,
                      ),
                    ),
                  ),
              ],
            ),

            // 1. Résultat Comptable Section
            _buildSectionCard(
              'Résultat Comptable',
              [
                _buildNumberInput(
                  _accountingResultController,
                  'Résultat comptable avant impôt',
                  hint: 'Saisissez le résultat comptable',
                ),
                Padding(
                  padding: const EdgeInsets.only(top: 8.0),
                  child: Text(
                    'Le résultat comptable est le point de départ pour déterminer le résultat fiscal.',
                    style: textTheme.bodySmall?.copyWith(
                      color: colorScheme.onSurfaceVariant,
                      fontStyle: FontStyle.italic,
                    ),
                  ),
                ),
              ],
            ),

            // 2. Réintégrations Section
            _buildSectionCard(
              'Réintégrations',
              [
                Text(
                  'Les réintégrations sont des charges comptabilisées mais non déductibles fiscalement.',
                  style: textTheme.bodyMedium?.copyWith(
                    color: colorScheme.onSurfaceVariant,
                    fontStyle: FontStyle.italic,
                  ),
                ),
                const SizedBox(height: 16),
                // Charges non déductibles fiscalement
                _buildSubsectionCard(
                  'Charges Non Déductibles',
                  [
                    _buildNumberInput(
                      _getControllerForItem('amendes'),
                      'Amendes et pénalités',
                      hint: 'Amendes, pénalités, majorations...',
                    ),
                    _buildNumberInput(
                      _getControllerForItem('impot_societe'),
                      'Impôt sur les sociétés',
                      hint: 'IS et rappels d\'IS',
                    ),
                    _buildNumberInput(
                      _getControllerForItem('penalites'),
                      'Pénalités et amendes fiscales',
                      hint: 'Pénalités de recouvrement, amendes fiscales...',
                    ),
                  ],
                ),
                // Charges sur exercice antérieur
                _buildSubsectionCard(
                  'Charges sur Exercice Antérieur',
                  [
                    _buildNumberInput(
                      _getControllerForItem('charges_anterieures'),
                      'Charges des exercices antérieurs',
                      hint:
                          'Charges qui auraient dû être comptabilisées sur les exercices antérieurs',
                    ),
                  ],
                ),
                // Charges non justifiées
                _buildSubsectionCard(
                  'Charges Non Justifiées',
                  [
                    _buildNumberInput(
                      _getControllerForItem('achats_non_justifies'),
                      'Achats non justifiés',
                      hint: 'Achats sans factures conformes',
                    ),
                    _buildNumberInput(
                      _getControllerForItem('services_non_justifies'),
                      'Services non justifiés',
                      hint: 'Prestations sans justificatifs conformes',
                    ),
                  ],
                ),
                // Amortissements
                _buildSubsectionCard(
                  'Amortissements Excédentaires',
                  [
                    _buildNumberInput(
                      _getControllerForItem('amort_vehicules'),
                      'Amortissements des véhicules',
                      hint: 'Part > 400.000 DH',
                    ),
                    _buildNumberInput(
                      _getControllerForItem('amort_taux_eleve'),
                      'Amortissements à taux élevé',
                      hint: 'Dépassement des taux fiscaux',
                    ),
                  ],
                ),
                // Provisions
                _buildSubsectionCard(
                  'Provisions',
                  [
                    _buildNumberInput(
                      _getControllerForItem('prov_clients'),
                      'Provisions clients',
                      hint: 'Provisions non conformes aux conditions fiscales',
                    ),
                    _buildNumberInput(
                      _getControllerForItem('prov_stocks'),
                      'Provisions pour dépréciation des stocks',
                      hint: 'Provisions non justifiées',
                    ),
                    _buildNumberInput(
                      _getControllerForItem('prov_risques'),
                      'Provisions pour risques et charges',
                      hint: 'Provisions non déductibles',
                    ),
                  ],
                ),
                // Charges financières
                _buildSubsectionCard(
                  'Charges Financières',
                  [
                    _buildNumberInput(
                      _getControllerForItem('interets_non_deductibles'),
                      'Intérêts non déductibles',
                      hint:
                          'Intérêts sur comptes courants dépassant les limites',
                    ),
                    _buildNumberInput(
                      _getControllerForItem('ecarts_conversion'),
                      'Écarts de conversion',
                      hint: 'Pertes de change latentes',
                    ),
                  ],
                ),
                // Autres réintégrations
                _buildSubsectionCard(
                  'Autres Réintégrations',
                  [
                    _buildNumberInput(
                      _getControllerForItem('dons'),
                      'Dons et libéralités',
                      hint: 'Dons dépassant les limites légales',
                    ),
                    _buildNumberInput(
                      _getControllerForItem('cadeaux'),
                      'Cadeaux excédentaires',
                      hint: 'Cadeaux > 100 DH par unité',
                    ),
                    _buildNumberInput(
                      _getControllerForItem('charges_personnel'),
                      'Charges de personnel non justifiées',
                      hint: 'Rémunérations non déclarées à la CNSS',
                    ),
                  ],
                ),
              ],
            ),

            // 3. Déductions Section
            _buildSectionCard(
              'Déductions',
              [
                Text(
                  'Les déductions sont des produits non imposables ou des charges déjà taxées.',
                  style: textTheme.bodyMedium?.copyWith(
                    color: colorScheme.onSurfaceVariant,
                    fontStyle: FontStyle.italic,
                  ),
                ),
                const SizedBox(height: 16),
                // Produits non imposables
                _buildSubsectionCard(
                  'Produits Non Imposables',
                  [
                    _buildNumberInput(
                      _getControllerForItem('dividendes'),
                      'Dividendes',
                      hint: 'Dividendes de sociétés soumises à l\'IS',
                    ),
                    _buildNumberInput(
                      _getControllerForItem('plus_values'),
                      'Plus-values sur cession',
                      hint: 'Plus-values non imposables',
                    ),
                    _buildNumberInput(
                      _getControllerForItem('revenus_source_etrangere'),
                      'Revenus de source étrangère',
                      hint: 'Revenus ayant supporté l\'impôt à l\'étranger',
                    ),
                  ],
                ),
                // Reprises et provisions
                _buildSubsectionCard(
                  'Reprises et Provisions',
                  [
                    _buildNumberInput(
                      _getControllerForItem('reprises_provisions'),
                      'Reprises sur provisions taxées',
                      hint: 'Provisions ayant été réintégrées',
                    ),
                    _buildNumberInput(
                      _getControllerForItem('reprises_amortissements'),
                      'Reprises sur amortissements',
                      hint: 'Amortissements réintégrés antérieurement',
                    ),
                  ],
                ),
                // Autres déductions
                _buildSubsectionCard(
                  'Autres Déductions',
                  [
                    _buildNumberInput(
                      _getControllerForItem('abattement_export'),
                      'Abattement sur export',
                      hint: 'Abattement sur chiffre d\'affaires à l\'export',
                    ),
                    _buildNumberInput(
                      _getControllerForItem('autres_deductions'),
                      'Autres déductions légales',
                      hint: 'Autres déductions prévues par la loi',
                    ),
                  ],
                ),
              ],
            ),

            // 4. Résultat Fiscal Section
            _buildSectionCard(
              'Résultat Fiscal',
              [
                ListTile(
                  title: Text(
                    'Résultat comptable',
                    style: textTheme.titleMedium?.copyWith(
                      color: colorScheme.onSurface,
                    ),
                  ),
                  trailing: Text(
                    '${_numberFormat.format(double.tryParse(_accountingResultController.text) ?? 0)} MAD',
                    style: textTheme.titleMedium?.copyWith(
                      color: colorScheme.onSurface,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
                ListTile(
                  title: Text(
                    '+ Total Réintégrations',
                    style: textTheme.titleMedium?.copyWith(
                      color: colorScheme.error,
                    ),
                  ),
                  trailing: Text(
                    '${_numberFormat.format(_totalReintegrations)} MAD',
                    style: textTheme.titleMedium?.copyWith(
                      color: colorScheme.error,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
                ListTile(
                  title: Text(
                    '- Total Déductions',
                    style: textTheme.titleMedium?.copyWith(
                      color: colorScheme.tertiary,
                    ),
                  ),
                  trailing: Text(
                    '${_numberFormat.format(_totalDeductions)} MAD',
                    style: textTheme.titleMedium?.copyWith(
                      color: colorScheme.tertiary,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
                const Divider(),
                ListTile(
                  title: Text(
                    'Résultat Fiscal',
                    style: textTheme.titleLarge?.copyWith(
                      color: colorScheme.primary,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  trailing: Text(
                    '${_numberFormat.format(_taxableBenefit)} MAD',
                    style: textTheme.titleLarge?.copyWith(
                      color: colorScheme.primary,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: Text(
                    _getAppliedTaxRateInfo(),
                    style: textTheme.bodyMedium?.copyWith(
                      color: colorScheme.primary,
                      fontStyle: FontStyle.italic,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              ],
            ),

            // 5. Calcul IS Section
            _buildSectionCard(
              'Calcul de l\'IS',
              [
                ListTile(
                  title: Text(
                    'Impôt sur les Sociétés (IS)',
                    style: textTheme.titleMedium?.copyWith(
                      color: colorScheme.onSurface,
                    ),
                  ),
                  trailing: Text(
                    '${_numberFormat.format(_isAmount)} MAD',
                    style: textTheme.titleMedium?.copyWith(
                      color: colorScheme.onSurface,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                ListTile(
                  title: Text(
                    'Cotisation Minimale (CM)',
                    style: textTheme.titleMedium?.copyWith(
                      color: colorScheme.onSurface,
                    ),
                  ),
                  trailing: Text(
                    '${_numberFormat.format(_cmAmount)} MAD',
                    style: textTheme.titleMedium?.copyWith(
                      color: colorScheme.onSurface,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                const Divider(),
                ListTile(
                  title: Text(
                    'Montant à payer',
                    style: textTheme.titleLarge?.copyWith(
                      color: colorScheme.primary,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  trailing: Text(
                    '${_numberFormat.format(_finalAmount)} MAD',
                    style: textTheme.titleLarge?.copyWith(
                      color: colorScheme.primary,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Text(
                    _getBusinessAnalysis(),
                    style: textTheme.bodyMedium?.copyWith(
                      color: colorScheme.onSurfaceVariant,
                    ),
                    textAlign: TextAlign.justify,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSubsectionCard(String title, List<Widget> children) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return Card(
      margin: const EdgeInsets.symmetric(vertical: 8.0),
      elevation: 0,
      color: colorScheme.surfaceContainerHighest.withOpacity(0.3),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: colorScheme.primary.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    Icons.arrow_right,
                    color: colorScheme.primary,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    title,
                    style: textTheme.titleMedium?.copyWith(
                      color: colorScheme.primary,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            ...children,
          ],
        ),
      ),
    );
  }

  // Helper method to build number input fields
  Widget _buildNumberInput(
    TextEditingController controller,
    String label, {
    String? hint,
  }) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: TextFormField(
        controller: controller,
        decoration: InputDecoration(
          labelText: label,
          hintText: hint,
          filled: true,
          fillColor: colorScheme.surfaceContainerHighest.withOpacity(0.3),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide.none,
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide(
              color: colorScheme.outline.withOpacity(0.3),
            ),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide(
              color: colorScheme.primary,
              width: 2,
            ),
          ),
          suffixText: ' MAD',
          suffixStyle: textTheme.bodyMedium?.copyWith(
            color: colorScheme.onSurfaceVariant,
          ),
          labelStyle: textTheme.bodyLarge?.copyWith(
            color: colorScheme.onSurface,
          ),
          floatingLabelStyle: textTheme.bodyLarge?.copyWith(
            color: colorScheme.primary,
          ),
          prefixIconColor: colorScheme.primary,
        ),
        style: textTheme.bodyLarge?.copyWith(
          color: colorScheme.onSurface,
        ),
        keyboardType: const TextInputType.numberWithOptions(decimal: true),
        inputFormatters: [
          FilteringTextInputFormatter.allow(RegExp(r'[0-9\.]')),
        ],
        onChanged: (value) {
          if (value.isNotEmpty) {
            String numericValue = value.replaceAll(RegExp(r'[^0-9\.]'), '');
            if (numericValue.split('.').length > 2) {
              numericValue = numericValue.replaceAll('.', '');
              numericValue = numericValue.replaceRange(
                  numericValue.length - 2, numericValue.length - 1, '.');
            }
            if (double.tryParse(numericValue) != null) {
              controller.value = TextEditingValue(
                text: numericValue,
                selection: TextSelection.collapsed(offset: numericValue.length),
              );
            }
          }
          _calculateTotals();
        },
      ),
    );
  }

  // Helper method to build section cards
  Widget _buildSectionCard(String title, List<Widget> children) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return Card(
      margin: const EdgeInsets.symmetric(vertical: 8.0),
      elevation: 2,
      surfaceTintColor: colorScheme.surfaceTint,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Container(
        decoration: BoxDecoration(
          color: colorScheme.surface,
          gradient: LinearGradient(
            colors: [
              colorScheme.surface,
              colorScheme.surfaceContainerHighest.withOpacity(0.3),
            ],
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
          ),
          borderRadius: BorderRadius.circular(16),
        ),
        child: Padding(
          padding: const EdgeInsets.all(20.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: colorScheme.primary.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Icon(
                      Icons.calculate,
                      color: colorScheme.primary,
                      size: 24,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Text(
                      title,
                      style: textTheme.titleLarge?.copyWith(
                        color: colorScheme.primary,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 20),
              ...children,
            ],
          ),
        ),
      ),
    );
  }

  // Helper method to get applied tax rate info
  String _getAppliedTaxRateInfo() {
    if (_taxableBenefit <= 0) return 'Pas d\'imposition';

    final regimes = _tauxData?['regimes'] as List<dynamic>? ?? [];
    final selectedRegime = regimes.firstWhere(
      (r) => r['name'] == _selectedSector,
      orElse: () => null,
    );

    if (selectedRegime == null) return '';

    String info = '';

    // Cas des tranches progressives
    if (selectedRegime.containsKey('tranches')) {
      final tranches = selectedRegime['tranches'] as List<dynamic>? ?? [];
      for (final tranche in tranches) {
        final min = tranche['min'] as num;
        final max = tranche['max'] as num?;
        final taux = tranche['taux'] as num;
        final commentaire = tranche['commentaire'] as String? ?? '';

        if (max == null) {
          if (_taxableBenefit > min) {
            info =
                'Taux appliqué : ${taux.toStringAsFixed(2)}% (Bénéfice > ${_numberFormat.format(min)} MAD)';
            if (commentaire.isNotEmpty) {
              info += '\n$commentaire';
            }
          }
        } else if (_taxableBenefit > min && _taxableBenefit <= max) {
          info =
              'Taux appliqué : ${taux.toStringAsFixed(2)}% (Tranche : ${_numberFormat.format(min)} - ${_numberFormat.format(max)} MAD)';
          if (commentaire.isNotEmpty) {
            info += '\n$commentaire';
          }
        }
      }
    }
    // Cas du taux unique
    else if (selectedRegime.containsKey('taux_unique')) {
      final taux = selectedRegime['taux_unique'] as num;
      info = 'Taux unique appliqué : ${taux.toStringAsFixed(2)}%';
    }
    // Cas de la progression (établissements financiers)
    else if (selectedRegime.containsKey('progression')) {
      final progression = selectedRegime['progression'] as List<dynamic>? ?? [];
      final currentYear = progression.firstWhere(
        (p) => p['annee'] == 2025,
        orElse: () => progression.last,
      );
      final taux = currentYear['taux'] as num;
      info = 'Taux appliqué : ${taux.toStringAsFixed(2)}% (Année 2025)';
    }

    return info;
  }

  // Helper method to get business analysis
  String _getBusinessAnalysis() {
    if (_taxableBenefit <= 0) {
      return 'Votre entreprise est en situation déficitaire. Pensez à reporter ce déficit sur les exercices ultérieurs.';
    }

    final ratio = _isAmount / _taxableBenefit * 100;
    String analysis = 'Analyse fiscale :\n';

    if (_cmAmount > _isAmount) {
      analysis += '• Vous êtes soumis à la cotisation minimale (CM > IS)\n';
      analysis +=
          '• Optimisez votre rentabilité pour dépasser le seuil de la CM';
    } else {
      analysis +=
          '• Taux effectif d\'imposition : ${ratio.toStringAsFixed(2)}%\n';
      if (ratio > 25) {
        analysis +=
            '• Envisagez des stratégies d\'optimisation fiscale légales';
      } else {
        analysis += '• Votre taux d\'imposition est optimal';
      }
    }

    return analysis;
  }

  // Helper method to get sector description
  String _getSectorDescription() {
    final regimes = _tauxData?['regimes'] as List<dynamic>? ?? [];
    final selectedRegime = regimes.firstWhere(
      (regime) => regime['name'] == _selectedSector,
      orElse: () => {'description': '', 'details': ''},
    );

    String description = '';

    // Description de base
    description = selectedRegime['description'] as String? ?? '';

    // Ajouter les détails selon le type de régime
    if (selectedRegime.containsKey('tranches')) {
      final tranches = selectedRegime['tranches'] as List<dynamic>? ?? [];
      description += '\n\nBarème :';
      for (final tranche in tranches) {
        final min = _numberFormat.format(tranche['min'] as num);
        final max = tranche['max'] != null
            ? _numberFormat.format(tranche['max'] as num)
            : '∞';
        final taux = (tranche['taux'] as num).toStringAsFixed(2);
        final commentaire = tranche['commentaire'] as String? ?? '';

        description += '\n• $taux% pour la tranche $min - $max MAD';
        if (commentaire.isNotEmpty) {
          description += ' ($commentaire)';
        }
      }
    } else if (selectedRegime.containsKey('taux_unique')) {
      final taux = selectedRegime['taux_unique'] as num;
      description += '\n\nTaux unique : ${taux.toStringAsFixed(2)}%';

      // Ajouter les secteurs concernés
      final secteurs = selectedRegime['secteurs'] as List<dynamic>? ?? [];
      if (secteurs.isNotEmpty) {
        description += '\n\nSecteurs concernés :';
        for (final secteur in secteurs) {
          description += '\n• ${secteur['nom']}';
          final conditions = secteur['conditions'] as List<dynamic>? ?? [];
          if (conditions.isNotEmpty) {
            description += '\n  Conditions :';
            for (final condition in conditions) {
              description += '\n  - $condition';
            }
          }
        }
      }
    } else if (selectedRegime.containsKey('progression')) {
      final progression = selectedRegime['progression'] as List<dynamic>? ?? [];
      description += '\n\nÉvolution du taux :';
      for (final prog in progression) {
        description += '\n• ${prog['annee']} : ${prog['taux']}%';
      }

      // Ajouter les établissements concernés
      final etablissements =
          selectedRegime['etablissements'] as List<dynamic>? ?? [];
      if (etablissements.isNotEmpty) {
        description += '\n\nÉtablissements concernés :';
        for (final etab in etablissements) {
          description += '\n• $etab';
        }
      }
    }

    return description;
  }

  @override
  void dispose() {
    // Dispose all controllers
    _accountingResultController.dispose();
    _regularRevenueController.dispose();
    _marginProductsRevenueController.dispose();
    _petroleumProductsRevenueController.dispose();

    // Dispose dynamic controllers
    _itemControllers?.forEach((_, controller) => controller.dispose());

    super.dispose();
  }
}
