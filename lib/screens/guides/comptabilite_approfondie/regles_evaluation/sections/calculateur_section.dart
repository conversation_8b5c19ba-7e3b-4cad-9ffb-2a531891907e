import 'package:flutter/material.dart';
import '../../../../../widgets/journal_comptable_widget.dart';
import 'package:flutter/services.dart';
import 'package:path_provider/path_provider.dart';
import 'package:open_file/open_file.dart';
import 'dart:io';
import 'package:excel/excel.dart' hide Border, BorderStyle;

class CalculateurSection extends StatefulWidget {
  const CalculateurSection({super.key});

  @override
  State<CalculateurSection> createState() => _CalculateurSectionState();
}

class _CalculateurSectionState extends State<CalculateurSection> {
  final _valeurOrigineController = TextEditingController();
  final _dureeController = TextEditingController();
  final _dateAcquisitionController = TextEditingController();
  final _modeAmortissementController = TextEditingController();
  final _tauxAmortissementController = TextEditingController();
  final _valeurResiduelleController = TextEditingController();
  final _typeImmobilisationController = TextEditingController();

  String? _resultatCalcul;
  List<Map<String, String>>? _tableauAmortissement;
  List<Map<String, String>>? _tableauDerogatoire;
  List<JournalEntry>? _ecrituresComptables;

  // Taux d'amortissement standards au Maroc
  final Map<String, double> _tauxStandards = {
    'Immeuble à usage d\'habitation ou commercial': 4,
    'Immeuble industriel': 5,
    'Construction légère': 10,
    'Matériel et installations': 10,
    'Matériel informatique': 20,
    'Mobilier et logiciels': 20,
    'Matériel roulant': 20,
    'Outillage': 30,
  };

  // Coefficients dégressifs selon la durée
  double _getCoefficient(int duree) {
    if (duree < 3) return 1.5; // Par défaut pour les durées courtes
    if (duree <= 4) return 1.5;
    if (duree <= 6) return 2;
    return 3; // Au-delà de 6 ans
  }

  // Formateur de nombres
  String _formatNumber(String value) {
    if (value.isEmpty) return '';
    final number = double.tryParse(value.replaceAll('.', ''));
    if (number == null) return value;
    return number.toStringAsFixed(0).replaceAllMapped(
        RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'), (Match m) => '${m[1]}.');
  }

  void _updateDureeFromTaux(double taux) {
    final duree = (100 / taux).round();
    _dureeController.text = duree.toString();
  }

  Widget _buildTypeImmobilisationField() {
    final colorScheme = Theme.of(context).colorScheme;
    return Tooltip(
      message:
          'Sélectionnez le type d\'immobilisation pour appliquer automatiquement le taux standard',
      waitDuration: const Duration(seconds: 1),
      child: DropdownButtonFormField<String>(
        isExpanded: true,
        decoration: InputDecoration(
          labelText: 'Type d\'immobilisation',
          filled: true,
          fillColor: colorScheme.surfaceContainerHighest.withOpacity(0.3),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide.none,
          ),
          prefixIcon: Icon(Icons.category, color: colorScheme.primary),
          contentPadding:
              const EdgeInsets.symmetric(horizontal: 12, vertical: 16),
        ),
        dropdownColor: colorScheme.surface,
        style: TextStyle(color: colorScheme.onSurface),
        icon: Icon(Icons.arrow_drop_down, color: colorScheme.primary),
        value: _typeImmobilisationController.text.isEmpty
            ? null
            : _typeImmobilisationController.text,
        items: _tauxStandards.keys.map<DropdownMenuItem<String>>((String type) {
          return DropdownMenuItem<String>(
            value: type,
            child: Text(type),
          );
        }).toList(),
        onChanged: (value) {
          setState(() {
            _typeImmobilisationController.text = value ?? '';
            if (value != null) {
              final taux = _tauxStandards[value]!;
              _tauxAmortissementController.text = taux.toString();
              _updateDureeFromTaux(taux);
            }
          });
        },
      ),
    );
  }

  Future<void> _selectDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime(2000),
      lastDate: DateTime(2100),
    );
    if (picked != null) {
      setState(() {
        _dateAcquisitionController.text =
            "${picked.day.toString().padLeft(2, '0')}/${picked.month.toString().padLeft(2, '0')}/${picked.year}";
      });
    }
  }

  Widget _buildModeAmortissementField() {
    final colorScheme = Theme.of(context).colorScheme;
    return Column(
      children: [
        Tooltip(
          message:
              'Le mode dégressif permet un amortissement plus rapide les premières années',
          waitDuration: const Duration(seconds: 1),
          child: DropdownButtonFormField<String>(
            decoration: InputDecoration(
              labelText: 'Mode d\'amortissement',
              filled: true,
              fillColor: colorScheme.surfaceContainerHighest.withOpacity(0.3),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide.none,
              ),
              prefixIcon: Icon(Icons.category, color: colorScheme.primary),
            ),
            dropdownColor: colorScheme.surface,
            style: TextStyle(color: colorScheme.onSurface),
            icon: Icon(Icons.arrow_drop_down, color: colorScheme.primary),
            value: _modeAmortissementController.text.isEmpty
                ? null
                : _modeAmortissementController.text,
            items: const [
              DropdownMenuItem(
                value: 'lineaire',
                child: Text('Linéaire (constant)'),
              ),
              DropdownMenuItem(
                value: 'degressif',
                child: Text('Dégressif'),
              ),
            ],
            onChanged: (value) {
              setState(() {
                _modeAmortissementController.text = value ?? '';
              });
            },
          ),
        ),
        if (_modeAmortissementController.text == 'degressif') ...[
          const SizedBox(height: 12),
          Text(
            'Coefficient dégressif : ${_getCoefficient(int.tryParse(_dureeController.text) ?? 0)}',
            style: TextStyle(
              color: colorScheme.onSurface,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ],
    );
  }

  @override
  void dispose() {
    _valeurOrigineController.dispose();
    _dureeController.dispose();
    _dateAcquisitionController.dispose();
    _modeAmortissementController.dispose();
    _tauxAmortissementController.dispose();
    _valeurResiduelleController.dispose();
    _typeImmobilisationController.dispose();
    super.dispose();
  }

  void _calculerAnnuites() {
    // Nettoyage des valeurs pour enlever les points de formatage
    final valeurOrigineStr = _valeurOrigineController.text.replaceAll('.', '');
    final valeurResiduelleStr =
        _valeurResiduelleController.text.replaceAll('.', '');

    // Parsing des valeurs avec vérification
    final valeurOrigine = double.tryParse(valeurOrigineStr);
    final duree = int.tryParse(_dureeController.text);
    final dateStr = _dateAcquisitionController.text;
    final mode = _modeAmortissementController.text;
    final tauxLineaire = double.tryParse(_tauxAmortissementController.text);
    final valeurResiduelle = double.tryParse(valeurResiduelleStr) ?? 0;

    // Validation des valeurs
    if (valeurOrigine == null) {
      _showErrorSnackBar('La valeur d\'origine n\'est pas valide');
      return;
    }
    if (duree == null || duree <= 0) {
      _showErrorSnackBar('La durée d\'amortissement n\'est pas valide');
      return;
    }
    if (dateStr.isEmpty) {
      _showErrorSnackBar('La date d\'acquisition est requise');
      return;
    }
    if (mode.isEmpty) {
      _showErrorSnackBar('Le mode d\'amortissement est requis');
      return;
    }
    if (tauxLineaire == null || tauxLineaire <= 0) {
      _showErrorSnackBar('Le taux d\'amortissement n\'est pas valide');
      return;
    }

    // Validation de la date
    final dateParts = dateStr.split('/');
    if (dateParts.length != 3) {
      _showErrorSnackBar('Format de date invalide (JJ/MM/AAAA)');
      return;
    }

    final jour = int.tryParse(dateParts[0]);
    final mois = int.tryParse(dateParts[1]);
    final annee = int.tryParse(dateParts[2]);

    if (jour == null ||
        mois == null ||
        annee == null ||
        jour < 1 ||
        jour > 31 ||
        mois < 1 ||
        mois > 12) {
      _showErrorSnackBar('Date invalide');
      return;
    }

    // Initialisation des variables de calcul
    final tableau = <Map<String, String>>[];
    final ecritures = <JournalEntry>[];

    double baseAmort = valeurOrigine - valeurResiduelle;
    double vnc = baseAmort;
    double cumulAmort = 0;

    final coefficient = _getCoefficient(duree);
    final tauxDegressif = tauxLineaire * coefficient;
    final moisRestants = 12 - (mois - 1);

    try {
      switch (mode) {
        case 'lineaire':
          final annuiteComplete = baseAmort * (tauxLineaire / 100);
          final premiereAnnuite = annuiteComplete * (moisRestants / 12);
          int anneeDebut = int.parse(dateParts[2]);

          // Première année
          tableau.add({
            'Année': '$anneeDebut ($moisRestants mois)',
            'Base': baseAmort.toStringAsFixed(2),
            'Taux': '${tauxLineaire.toString()} %',
            'Annuités': premiereAnnuite.toStringAsFixed(2),
            'Annuités cumulées': premiereAnnuite.toStringAsFixed(2),
            'VNC': (baseAmort - premiereAnnuite).toStringAsFixed(2),
          });

          ecritures.add(
            JournalEntry(
              date: '31/12/$anneeDebut',
              lines: [
                JournalLine(
                  account: '6193',
                  label: 'Dotations aux amortissements',
                  debit: premiereAnnuite.toStringAsFixed(2),
                ),
                JournalLine(
                  account: '2832',
                  label: 'Amortissements des installations techniques',
                  credit: premiereAnnuite.toStringAsFixed(2),
                ),
              ],
            ),
          );

          vnc -= premiereAnnuite;
          cumulAmort = premiereAnnuite;

          // Années suivantes
          for (var i = 1; i < duree; i++) {
            final annuitePeriode = i == duree - 1 ? vnc : annuiteComplete;
            cumulAmort += annuitePeriode;
            vnc -= annuitePeriode;
            final anneeActuelle = anneeDebut + i;

            tableau.add({
              'Année': i == duree - 1
                  ? '$anneeActuelle (${12 - moisRestants} mois)'
                  : anneeActuelle.toString(),
              'Base': baseAmort.toStringAsFixed(2),
              'Taux': '${tauxLineaire.toString()} %',
              'Annuités': annuitePeriode.toStringAsFixed(2),
              'Annuités cumulées': cumulAmort.toStringAsFixed(2),
              'VNC': i == duree - 1
                  ? '${valeurResiduelle.toStringAsFixed(2)} (valeur résiduelle)'
                  : vnc.toStringAsFixed(2),
            });

            ecritures.add(
              JournalEntry(
                date: '31/12/$anneeActuelle',
                lines: [
                  JournalLine(
                    account: '6193',
                    label: 'Dotations aux amortissements',
                    debit: annuitePeriode.toStringAsFixed(2),
                  ),
                  JournalLine(
                    account: '2832',
                    label: 'Amortissements des installations techniques',
                    credit: annuitePeriode.toStringAsFixed(2),
                  ),
                ],
              ),
            );
          }

          setState(() {
            _resultatCalcul = 'Calcul effectué avec succès.';
            _tableauAmortissement = tableau;
            _tableauDerogatoire =
                null; // Reset derogatory table for linear mode
            _ecrituresComptables = ecritures;
          });
          break;

        case 'degressif':
          // Calcul du taux dégressif initial
          final tauxDegressif = tauxLineaire * coefficient;
          double baseAmortDegressif = baseAmort;
          double cumulAmort = 0;

          // Pour le tableau dérogatoire
          List<Map<String, String>> tableauDerogatoire = [];
          int anneeDebut = int.parse(dateParts[2]);

          for (var i = 0; i < duree; i++) {
            final anneeActuelle = anneeDebut + i;
            // Calcul du taux linéaire restant (100/nombre d'années restantes)
            final tauxLineaireRestant = 100.0 / (duree - i);

            // Calcul de l'annuité dégressive
            double annuiteDegressive =
                baseAmortDegressif * (tauxDegressif / 100);
            double annuiteLineaire =
                baseAmortDegressif * (tauxLineaireRestant / 100);

            // On prend la plus grande des deux annuités
            double annuite = annuiteDegressive > annuiteLineaire
                ? annuiteDegressive
                : annuiteLineaire;

            // Pour la dernière année, on amortit le reste
            if (i == duree - 1) {
              annuite = baseAmortDegressif;
            }

            cumulAmort += annuite;

            // Calcul pour le tableau principal
            tableau.add({
              'Année': i == 0
                  ? '$anneeActuelle ($moisRestants mois)'
                  : (i == duree - 1
                      ? '$anneeActuelle (${12 - moisRestants} mois)'
                      : anneeActuelle.toString()),
              'Base': baseAmortDegressif.toStringAsFixed(2),
              'Taux dégressif': '${tauxDegressif.toStringAsFixed(2)} %',
              'Taux linéaire': '${tauxLineaireRestant.toStringAsFixed(2)} %',
              'Annuités': annuite.toStringAsFixed(2),
              'Annuités cumulées': cumulAmort.toStringAsFixed(2),
              'VNC': i == duree - 1
                  ? '${valeurResiduelle.toStringAsFixed(2)} (valeur résiduelle)'
                  : (baseAmort - cumulAmort).toStringAsFixed(2),
            });

            // Calcul pour le tableau dérogatoire
            final amortComptable = baseAmort *
                (tauxLineaire /
                    100); // Amortissement linéaire avec taux constant
            final amortFiscal =
                annuite; // On utilise l'annuité calculée comme amortissement fiscal

            // Si l'amortissement fiscal est supérieur au comptable -> dotation
            // Si l'amortissement fiscal est inférieur au comptable -> reprise
            final dotation = amortFiscal > amortComptable
                ? amortFiscal - amortComptable
                : 0.00;
            final reprise = amortFiscal < amortComptable
                ? amortComptable - amortFiscal
                : 0.00;

            tableauDerogatoire.add({
              'Année': i == 0
                  ? '$anneeActuelle ($moisRestants mois)'
                  : (i == duree - 1
                      ? '$anneeActuelle (${12 - moisRestants} mois)'
                      : anneeActuelle.toString()),
              'Amortissement comptable': amortComptable.toStringAsFixed(2),
              'Amortissement fiscal': amortFiscal.toStringAsFixed(2),
              'Dotations': dotation > 0 ? dotation.toStringAsFixed(2) : '-',
              'Reprises': reprise > 0 ? reprise.toStringAsFixed(2) : '-',
            });

            // Mise à jour de la base pour l'année suivante
            baseAmortDegressif -= annuite;

            // Écritures comptables normales
            ecritures.add(
              JournalEntry(
                date: '31/12/$anneeActuelle',
                lines: [
                  JournalLine(
                    account: '6193',
                    label: 'Dotations aux amortissements',
                    debit: amortComptable.toStringAsFixed(2),
                  ),
                  JournalLine(
                    account: '2832',
                    label: 'Amortissements des installations techniques',
                    credit: amortComptable.toStringAsFixed(2),
                  ),
                ],
              ),
            );

            // Écritures dérogatoires
            if (dotation > 0) {
              ecritures.add(
                JournalEntry(
                  date: '31/12/$anneeActuelle',
                  lines: [
                    JournalLine(
                      account: '65941',
                      label: 'D.N.C. pour amortissements dérogatoires',
                      debit: dotation.toStringAsFixed(2),
                    ),
                    JournalLine(
                      account: '1351',
                      label: 'Provisions pour amortissements dérogatoires',
                      credit: dotation.toStringAsFixed(2),
                    ),
                  ],
                ),
              );
            } else if (reprise > 0) {
              ecritures.add(
                JournalEntry(
                  date: '31/12/$anneeActuelle',
                  lines: [
                    JournalLine(
                      account: '1351',
                      label: 'Provisions pour amortissements dérogatoires',
                      debit: reprise.toStringAsFixed(2),
                    ),
                    JournalLine(
                      account: '75941',
                      label: 'Reprises sur amortissements dérogatoires',
                      credit: reprise.toStringAsFixed(2),
                    ),
                  ],
                ),
              );
            }
          }

          setState(() {
            _resultatCalcul = 'Calcul effectué avec succès.';
            _tableauAmortissement = tableau;
            _tableauDerogatoire = tableauDerogatoire;
            _ecrituresComptables = ecritures;
          });
          break;
      }
    } catch (e) {
      _showErrorSnackBar('Une erreur est survenue lors du calcul');
      print('Erreur de calcul: $e');
    }
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Theme.of(context).colorScheme.error,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  void _validateAndCalculate() {
    // Vérification des champs requis
    if (_valeurOrigineController.text.isEmpty) {
      _showErrorSnackBar('Veuillez saisir la valeur d\'origine');
      return;
    }
    if (_typeImmobilisationController.text.isEmpty) {
      _showErrorSnackBar('Veuillez sélectionner le type d\'immobilisation');
      return;
    }
    if (_dateAcquisitionController.text.isEmpty) {
      _showErrorSnackBar('Veuillez sélectionner la date d\'acquisition');
      return;
    }
    if (_modeAmortissementController.text.isEmpty) {
      _showErrorSnackBar('Veuillez sélectionner le mode d\'amortissement');
      return;
    }

    _calculerAnnuites();
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;
    final isDesktop = MediaQuery.of(context).size.width >= 600;

    return SingleChildScrollView(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    colorScheme.primary,
                    colorScheme.primaryContainer,
                  ],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.circular(16),
                boxShadow: [
                  BoxShadow(
                    color: colorScheme.shadow.withOpacity(0.1),
                    blurRadius: 10,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Calculateur d\'amortissements',
                    style: textTheme.headlineMedium?.copyWith(
                      color: colorScheme.onPrimary,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Calculez vos amortissements en quelques clics',
                    style: textTheme.titleMedium?.copyWith(
                      color: colorScheme.onPrimary.withOpacity(0.8),
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 24),
            Card(
              elevation: 2,
              surfaceTintColor: colorScheme.surfaceTint,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              child: Padding(
                padding: const EdgeInsets.all(20),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    _buildTypeImmobilisationField(),
                    const SizedBox(height: 12),
                    _buildValeurOrigineField(colorScheme),
                    const SizedBox(height: 12),
                    _buildDateAcquisitionField(colorScheme),
                    const SizedBox(height: 12),
                    _buildDureeField(colorScheme),
                    const SizedBox(height: 12),
                    _buildModeAmortissementField(),
                    const SizedBox(height: 12),
                    _buildTauxAmortissementField(colorScheme),
                    const SizedBox(height: 12),
                    _buildValeurResiduelleField(colorScheme),
                    const SizedBox(height: 24),
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton.icon(
                        onPressed: _validateAndCalculate,
                        icon: const Icon(Icons.calculate),
                        label: Text('Calculer l\'amortissement',
                            style: textTheme.labelLarge
                                ?.copyWith(color: colorScheme.onPrimary)),
                      ),
                    ),
                  ],
                ),
              ),
            ),
            if (_resultatCalcul != null && _resultatCalcul!.isNotEmpty) ...[
              const SizedBox(height: 24),
              _buildResultatCalcul(),
            ],
            if (_tableauAmortissement != null &&
                _tableauAmortissement!.isNotEmpty) ...[
              const SizedBox(height: 24),
              _buildTableauAmortissement(),
              if (_modeAmortissementController.text == 'degressif') ...[
                const SizedBox(height: 24),
                _buildTableauDerogatoire(),
              ],
            ],
            if (_ecrituresComptables != null &&
                _ecrituresComptables!.isNotEmpty) ...[
              const SizedBox(height: 24),
              _buildEcrituresComptables(),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildValeurOrigineField(ColorScheme colorScheme) {
    final textTheme = Theme.of(context).textTheme;
    return Tooltip(
      message: 'Entrez le coût d\'acquisition de l\'immobilisation',
      waitDuration: const Duration(seconds: 1),
      child: TextFormField(
        controller: _valeurOrigineController,
        decoration: InputDecoration(
          labelText: 'Valeur d\'origine (DH)',
          filled: true,
          fillColor: colorScheme.surfaceContainerHighest.withOpacity(0.3),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide.none,
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide(
              color: colorScheme.outline.withOpacity(0.3),
            ),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide(
              color: colorScheme.primary,
              width: 2,
            ),
          ),
          prefixIcon: Icon(Icons.attach_money, color: colorScheme.primary),
          suffixText: ' MAD',
          suffixStyle: textTheme.bodyMedium?.copyWith(
            color: colorScheme.onSurfaceVariant,
          ),
          labelStyle: textTheme.bodyLarge?.copyWith(
            color: colorScheme.onSurface,
          ),
          floatingLabelStyle: textTheme.bodyLarge?.copyWith(
            color: colorScheme.primary,
          ),
          hintStyle: textTheme.bodyMedium?.copyWith(
            color: colorScheme.onSurfaceVariant.withOpacity(0.8),
          ),
        ),
        style: textTheme.bodyLarge?.copyWith(
          color: colorScheme.onSurface,
        ),
        keyboardType: TextInputType.number,
        onChanged: (value) {
          final formattedValue = _formatNumber(value);
          if (formattedValue != value) {
            _valeurOrigineController.value = TextEditingValue(
              text: formattedValue,
              selection: TextSelection.collapsed(offset: formattedValue.length),
            );
          }
        },
      ),
    );
  }

  Widget _buildDateAcquisitionField(ColorScheme colorScheme) {
    final textTheme = Theme.of(context).textTheme;
    return TextFormField(
      controller: _dateAcquisitionController,
      decoration: InputDecoration(
        labelText: 'Date d\'acquisition',
        filled: true,
        fillColor: colorScheme.surfaceContainerHighest.withOpacity(0.3),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide.none,
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(
            color: colorScheme.outline.withOpacity(0.3),
          ),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(
            color: colorScheme.primary,
            width: 2,
          ),
        ),
        prefixIcon: Icon(Icons.calendar_today, color: colorScheme.primary),
        suffixIcon: IconButton(
          icon: Icon(
            Icons.edit_calendar,
            color: colorScheme.primary,
          ),
          onPressed: () => _selectDate(context),
          tooltip: 'Sélectionner une date',
        ),
        labelStyle: textTheme.bodyLarge?.copyWith(
          color: colorScheme.onSurface,
        ),
        floatingLabelStyle: textTheme.bodyLarge?.copyWith(
          color: colorScheme.primary,
        ),
        hintStyle: textTheme.bodyMedium?.copyWith(
          color: colorScheme.onSurfaceVariant.withOpacity(0.8),
        ),
      ),
      style: textTheme.bodyLarge?.copyWith(
        color: colorScheme.onSurface,
      ),
      readOnly: true,
      onTap: () => _selectDate(context),
    );
  }

  Widget _buildDureeField(ColorScheme colorScheme) {
    final textTheme = Theme.of(context).textTheme;
    return TextFormField(
      controller: _dureeController,
      decoration: InputDecoration(
        labelText: 'Durée d\'utilisation (années)',
        filled: true,
        fillColor: colorScheme.surfaceContainerHighest.withOpacity(0.3),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide.none,
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(
            color: colorScheme.outline.withOpacity(0.3),
          ),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(
            color: colorScheme.primary,
            width: 2,
          ),
        ),
        prefixIcon: Icon(Icons.timer, color: colorScheme.primary),
        suffixText: ' ans',
        suffixStyle: textTheme.bodyMedium?.copyWith(
          color: colorScheme.onSurfaceVariant,
        ),
        labelStyle: textTheme.bodyLarge?.copyWith(
          color: colorScheme.onSurface,
        ),
        floatingLabelStyle: textTheme.bodyLarge?.copyWith(
          color: colorScheme.primary,
        ),
      ),
      style: textTheme.bodyLarge?.copyWith(
        color: colorScheme.onSurface,
      ),
      keyboardType: TextInputType.number,
    );
  }

  Widget _buildTauxAmortissementField(ColorScheme colorScheme) {
    final textTheme = Theme.of(context).textTheme;
    return TextFormField(
      controller: _tauxAmortissementController,
      decoration: InputDecoration(
        labelText: 'Taux d\'amortissement (%)',
        filled: true,
        fillColor: colorScheme.surfaceContainerHighest.withOpacity(0.3),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide.none,
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(
            color: colorScheme.outline.withOpacity(0.3),
          ),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(
            color: colorScheme.primary,
            width: 2,
          ),
        ),
        prefixIcon: Icon(Icons.percent, color: colorScheme.primary),
        suffixText: ' %',
        suffixStyle: textTheme.bodyMedium?.copyWith(
          color: colorScheme.onSurfaceVariant,
        ),
        labelStyle: textTheme.bodyLarge?.copyWith(
          color: colorScheme.onSurface,
        ),
        floatingLabelStyle: textTheme.bodyLarge?.copyWith(
          color: colorScheme.primary,
        ),
      ),
      style: textTheme.bodyLarge?.copyWith(
        color: colorScheme.onSurface,
      ),
      keyboardType: TextInputType.number,
      onChanged: (value) {
        if (value.isNotEmpty) {
          final taux = double.tryParse(value);
          if (taux != null) {
            _updateDureeFromTaux(taux);
          }
        }
      },
    );
  }

  Widget _buildValeurResiduelleField(ColorScheme colorScheme) {
    final textTheme = Theme.of(context).textTheme;
    return TextFormField(
      controller: _valeurResiduelleController,
      decoration: InputDecoration(
        labelText: 'Valeur résiduelle (DH)',
        filled: true,
        fillColor: colorScheme.surfaceContainerHighest.withOpacity(0.3),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide.none,
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(
            color: colorScheme.outline.withOpacity(0.3),
          ),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(
            color: colorScheme.primary,
            width: 2,
          ),
        ),
        prefixIcon: Icon(Icons.money_off, color: colorScheme.primary),
        suffixText: ' MAD',
        suffixStyle: textTheme.bodyMedium?.copyWith(
          color: colorScheme.onSurfaceVariant,
        ),
        labelStyle: textTheme.bodyLarge?.copyWith(
          color: colorScheme.onSurface,
        ),
        floatingLabelStyle: textTheme.bodyLarge?.copyWith(
          color: colorScheme.primary,
        ),
      ),
      style: textTheme.bodyLarge?.copyWith(
        color: colorScheme.onSurface,
      ),
      keyboardType: TextInputType.number,
      onChanged: (value) {
        final formattedValue = _formatNumber(value);
        if (formattedValue != value) {
          _valeurResiduelleController.value = TextEditingValue(
            text: formattedValue,
            selection: TextSelection.collapsed(offset: formattedValue.length),
          );
        }
      },
    );
  }

  Widget _buildResultatCalcul() {
    return Text(_resultatCalcul!);
  }

  Widget _buildTableauAmortissement() {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    if (_tableauAmortissement == null || _tableauAmortissement!.isEmpty) {
      return SizedBox(
        width: double.infinity,
        height: 100,
        child: Center(
          child: Text(
            'Aucun calcul d\'amortissement effectué',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color:
                      Theme.of(context).colorScheme.onSurface.withOpacity(0.5),
                ),
          ),
        ),
      );
    }

    final isLineaire = _modeAmortissementController.text == 'lineaire';

    return Card(
      elevation: 2,
      surfaceTintColor: colorScheme.surfaceTint,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Tableau d\'amortissement',
                  style: textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: colorScheme.primary,
                  ),
                ),
                Row(
                  children: [
                    IconButton(
                      icon: const Icon(Icons.copy),
                      tooltip: 'Copier le tableau',
                      color: colorScheme.primary,
                      onPressed: () =>
                          _copyTableToClipboard(_tableauAmortissement!),
                    ),
                    IconButton(
                      icon: const Icon(Icons.file_download),
                      tooltip: 'Exporter en Excel',
                      color: colorScheme.primary,
                      onPressed: () => _exportToExcel(
                          _tableauAmortissement!, 'amortissement'),
                    ),
                  ],
                ),
              ],
            ),
          ),
          SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Theme(
              data: Theme.of(context).copyWith(
                dataTableTheme: DataTableThemeData(
                  headingTextStyle: TextStyle(
                    color: colorScheme.primary,
                    fontWeight: FontWeight.bold,
                  ),
                  dataTextStyle: TextStyle(
                    color: colorScheme.onSurface,
                  ),
                ),
              ),
              child: DataTable(
                columnSpacing: 16,
                horizontalMargin: 16,
                columns: isLineaire
                    ? const [
                        DataColumn(
                          label: Text(
                            'Année',
                            textAlign: TextAlign.center,
                            style: TextStyle(fontWeight: FontWeight.bold),
                          ),
                        ),
                        DataColumn(
                          label: Text(
                            'Base\nd\'amortissement',
                            textAlign: TextAlign.center,
                            style: TextStyle(fontWeight: FontWeight.bold),
                          ),
                        ),
                        DataColumn(
                          label: Text(
                            'Taux',
                            textAlign: TextAlign.center,
                            style: TextStyle(fontWeight: FontWeight.bold),
                          ),
                        ),
                        DataColumn(
                          label: Text(
                            'Annuités',
                            textAlign: TextAlign.center,
                            style: TextStyle(fontWeight: FontWeight.bold),
                          ),
                        ),
                        DataColumn(
                          label: Text(
                            'Annuités\ncumulées',
                            textAlign: TextAlign.center,
                            style: TextStyle(fontWeight: FontWeight.bold),
                          ),
                        ),
                        DataColumn(
                          label: Text(
                            'Valeur nette\ncomptable',
                            textAlign: TextAlign.center,
                            style: TextStyle(fontWeight: FontWeight.bold),
                          ),
                        ),
                      ]
                    : const [
                        DataColumn(
                          label: Text(
                            'Année',
                            textAlign: TextAlign.center,
                            style: TextStyle(fontWeight: FontWeight.bold),
                          ),
                        ),
                        DataColumn(
                          label: Text(
                            'Base\nd\'amortissement',
                            textAlign: TextAlign.center,
                            style: TextStyle(fontWeight: FontWeight.bold),
                          ),
                        ),
                        DataColumn(
                          label: Text(
                            'Taux\ndégressif',
                            textAlign: TextAlign.center,
                            style: TextStyle(fontWeight: FontWeight.bold),
                          ),
                        ),
                        DataColumn(
                          label: Text(
                            'Taux\nlinéaire',
                            textAlign: TextAlign.center,
                            style: TextStyle(fontWeight: FontWeight.bold),
                          ),
                        ),
                        DataColumn(
                          label: Text(
                            'Annuités\nd\'amortissement',
                            textAlign: TextAlign.center,
                            style: TextStyle(fontWeight: FontWeight.bold),
                          ),
                        ),
                        DataColumn(
                          label: Text(
                            'Annuités\ncumulées',
                            textAlign: TextAlign.center,
                            style: TextStyle(fontWeight: FontWeight.bold),
                          ),
                        ),
                        DataColumn(
                          label: Text(
                            'Valeur nette\ncomptable',
                            textAlign: TextAlign.center,
                            style: TextStyle(fontWeight: FontWeight.bold),
                          ),
                        ),
                      ],
                rows: _tableauAmortissement!
                    .map((ligne) => DataRow(
                          cells: isLineaire
                              ? [
                                  DataCell(Text(ligne['Année'] ?? '',
                                      textAlign: TextAlign.right)),
                                  DataCell(Text(ligne['Base'] ?? '',
                                      textAlign: TextAlign.right)),
                                  DataCell(Text(ligne['Taux'] ?? '',
                                      textAlign: TextAlign.right)),
                                  DataCell(Text(ligne['Annuités'] ?? '',
                                      textAlign: TextAlign.right)),
                                  DataCell(Text(
                                      ligne['Annuités cumulées'] ?? '',
                                      textAlign: TextAlign.right)),
                                  DataCell(Text(ligne['VNC'] ?? '',
                                      textAlign: TextAlign.right)),
                                ]
                              : [
                                  DataCell(Text(ligne['Année'] ?? '',
                                      textAlign: TextAlign.right)),
                                  DataCell(Text(ligne['Base'] ?? '',
                                      textAlign: TextAlign.right)),
                                  DataCell(Text(ligne['Taux dégressif'] ?? '',
                                      textAlign: TextAlign.right)),
                                  DataCell(Text(ligne['Taux linéaire'] ?? '',
                                      textAlign: TextAlign.right)),
                                  DataCell(Text(ligne['Annuités'] ?? '',
                                      textAlign: TextAlign.right)),
                                  DataCell(Text(
                                      ligne['Annuités cumulées'] ?? '',
                                      textAlign: TextAlign.right)),
                                  DataCell(Text(ligne['VNC'] ?? '',
                                      textAlign: TextAlign.right)),
                                ],
                        ))
                    .toList(),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTableauDerogatoire() {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    if (_tableauDerogatoire == null ||
        _tableauDerogatoire!.isEmpty ||
        _modeAmortissementController.text != 'degressif') {
      return Container();
    }
    return Card(
      elevation: 2,
      surfaceTintColor: colorScheme.surfaceTint,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Amortissement dérogatoire',
                  style: textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: colorScheme.primary,
                  ),
                ),
                Row(
                  children: [
                    IconButton(
                      icon: const Icon(Icons.copy),
                      tooltip: 'Copier le tableau',
                      color: colorScheme.primary,
                      onPressed: () =>
                          _copyTableToClipboard(_tableauDerogatoire!),
                    ),
                    IconButton(
                      icon: const Icon(Icons.file_download),
                      tooltip: 'Exporter en Excel',
                      color: colorScheme.primary,
                      onPressed: () =>
                          _exportToExcel(_tableauDerogatoire!, 'derogatoire'),
                    ),
                  ],
                ),
              ],
            ),
          ),
          SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Theme(
              data: Theme.of(context).copyWith(
                dataTableTheme: DataTableThemeData(
                  headingTextStyle: TextStyle(
                    color: colorScheme.primary,
                    fontWeight: FontWeight.bold,
                  ),
                  dataTextStyle: TextStyle(
                    color: colorScheme.onSurface,
                  ),
                ),
              ),
              child: DataTable(
                columnSpacing: 16,
                horizontalMargin: 16,
                columns: const [
                  DataColumn(
                    label: Text(
                      'Année',
                      textAlign: TextAlign.center,
                      style: TextStyle(fontWeight: FontWeight.bold),
                    ),
                  ),
                  DataColumn(
                    label: Text(
                      'Amortissement\ncomptable',
                      textAlign: TextAlign.center,
                      style: TextStyle(fontWeight: FontWeight.bold),
                    ),
                  ),
                  DataColumn(
                    label: Text(
                      'Amortissement\nfiscal',
                      textAlign: TextAlign.center,
                      style: TextStyle(fontWeight: FontWeight.bold),
                    ),
                  ),
                  DataColumn(
                    label: Text(
                      'Dotations',
                      textAlign: TextAlign.center,
                      style: TextStyle(fontWeight: FontWeight.bold),
                    ),
                  ),
                  DataColumn(
                    label: Text(
                      'Reprises',
                      textAlign: TextAlign.center,
                      style: TextStyle(fontWeight: FontWeight.bold),
                    ),
                  ),
                ],
                rows: _tableauDerogatoire!
                    .map((ligne) => DataRow(
                          cells: [
                            DataCell(Text(ligne['Année'] ?? '',
                                textAlign: TextAlign.right)),
                            DataCell(Text(
                                ligne['Amortissement comptable'] ?? '',
                                textAlign: TextAlign.right)),
                            DataCell(Text(ligne['Amortissement fiscal'] ?? '',
                                textAlign: TextAlign.right)),
                            DataCell(Text(ligne['Dotations'] ?? '',
                                textAlign: TextAlign.right)),
                            DataCell(Text(ligne['Reprises'] ?? '',
                                textAlign: TextAlign.right)),
                          ],
                        ))
                    .toList(),
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _copyTableToClipboard(List<Map<String, String>> table) {
    final StringBuffer buffer = StringBuffer();

    // Add headers
    buffer.writeln(table.first.keys.join('\t'));

    // Add data rows
    for (var row in table) {
      buffer.writeln(row.values.join('\t'));
    }

    Clipboard.setData(ClipboardData(text: buffer.toString()));
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Tableau copié dans le presse-papiers'),
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  Future<void> _exportToExcel(
      List<Map<String, String>> table, String type) async {
    try {
      final excel = Excel.createExcel();
      final sheet = excel[excel.getDefaultSheet()!];

      // Définir les en-têtes
      var col = 0;
      for (var header in table.first.keys) {
        var cell = sheet
            .cell(CellIndex.indexByColumnRow(columnIndex: col, rowIndex: 0));
        cell.value = TextCellValue(header);
        cell.cellStyle = CellStyle(
          bold: true,
          horizontalAlign: HorizontalAlign.Center,
          verticalAlign: VerticalAlign.Center,
        );
        col++;
      }

      // Ajouter les données
      for (var rowIndex = 0; rowIndex < table.length; rowIndex++) {
        var row = table[rowIndex];
        col = 0;
        for (var value in row.values) {
          var cell = sheet.cell(CellIndex.indexByColumnRow(
              columnIndex: col, rowIndex: rowIndex + 1));

          // Style de base pour toutes les cellules
          var cellStyle = CellStyle(
            horizontalAlign: HorizontalAlign.Right,
          );

          // Si c'est la dernière ligne, ajouter un style spécial
          if (rowIndex == table.length - 1) {
            cellStyle = CellStyle(
              bold: true,
              horizontalAlign: HorizontalAlign.Right,
            );
          }

          // Si la valeur contient un nombre, le convertir en nombre pour Excel
          if (value.contains(RegExp(r'[0-9]'))) {
            String numStr = value.replaceAll(' ', '');
            // Garder le symbole % mais enlever les espaces
            if (numStr.contains('%')) {
              numStr = numStr.replaceAll('%', '');
              double? num = double.tryParse(numStr);
              if (num != null) {
                cell.value = DoubleCellValue(num / 100);
                cell.cellStyle = cellStyle;
              } else {
                cell.value = TextCellValue(value);
                cell.cellStyle = cellStyle;
              }
            } else {
              double? num = double.tryParse(numStr);
              if (num != null) {
                cell.value = DoubleCellValue(num);
                cell.cellStyle = cellStyle;
              } else {
                cell.value = TextCellValue(value);
                cell.cellStyle = cellStyle;
              }
            }
          } else {
            // Pour la colonne Année, aligner à gauche
            if (col == 0) {
              cellStyle = CellStyle(
                horizontalAlign: HorizontalAlign.Left,
              );
            }
            cell.value = TextCellValue(value);
            cell.cellStyle = cellStyle;
          }
          col++;
        }
      }

      // Ajuster la largeur des colonnes
      for (var i = 0; i < table.first.keys.length; i++) {
        sheet.setColumnWidth(
            i, 20); // Augmenter la largeur pour une meilleure lisibilité
      }
      // Première colonne (Année) un peu moins large
      sheet.setColumnWidth(0, 15);

      final now = DateTime.now();
      final fileName =
          'amortissement_${type}_${now.year}${now.month}${now.day}_${now.hour}${now.minute}.xlsx';
      final directory = await getApplicationDocumentsDirectory();
      final file = File('${directory.path}/$fileName');

      // Sauvegarder le fichier Excel
      final excelBytes = excel.encode();
      if (excelBytes != null) {
        await file.writeAsBytes(excelBytes);

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Fichier exporté : $fileName'),
            behavior: SnackBarBehavior.floating,
            action: SnackBarAction(
              label: 'Ouvrir',
              onPressed: () => OpenFile.open(file.path),
            ),
          ),
        );
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Erreur lors de l\'export : $e'),
          behavior: SnackBarBehavior.floating,
          backgroundColor: Theme.of(context).colorScheme.error,
        ),
      );
    }
  }

  Widget _buildEcrituresComptables() {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    if (_ecrituresComptables == null || _ecrituresComptables!.isEmpty) {
      return Container(); // Return an empty container if there's no data
    }
    return Card(
      elevation: 2,
      surfaceTintColor: colorScheme.surfaceTint,
      child: JournalComptableWidget(
        title: 'Écritures comptables',
        entries: _ecrituresComptables!,
      ),
    );
  }
}
