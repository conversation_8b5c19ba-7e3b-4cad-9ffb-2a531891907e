import 'package:flutter/services.dart';

/// Comprehensive input validation utilities for tax calculators
class InputValidators {
  // Common error messages
  static const String requiredFieldError = 'Ce champ est obligatoire';
  static const String invalidNumberError = 'Veuillez saisir un nombre valide';
  static const String negativeNumberError = 'La valeur ne peut pas être négative';
  static const String invalidPercentageError = 'Le pourcentage doit être entre 0 et 100';
  static const String invalidTaxRateError = 'Le taux d\'impôt doit être entre 0 et 50';
  static const String maxValueExceededError = 'La valeur dépasse le maximum autorisé';
  static const String minValueError = 'La valeur est inférieure au minimum requis';

  /// Validate monetary amount (salary, revenue, etc.)
  static String? validateMonetaryAmount(String? value, {
    bool required = true,
    double? minValue,
    double? maxValue,
    String? customErrorMessage,
  }) {
    if (value == null || value.trim().isEmpty) {
      return required ? (customErrorMessage ?? requiredFieldError) : null;
    }

    final cleanValue = value.replaceAll(RegExp(r'[^\d.,]'), '').replaceAll(',', '.');
    final parsedValue = double.tryParse(cleanValue);

    if (parsedValue == null) {
      return customErrorMessage ?? invalidNumberError;
    }

    if (parsedValue < 0) {
      return customErrorMessage ?? negativeNumberError;
    }

    if (minValue != null && parsedValue < minValue) {
      return customErrorMessage ?? '$minValueError (minimum: ${minValue.toStringAsFixed(2)})';
    }

    if (maxValue != null && parsedValue > maxValue) {
      return customErrorMessage ?? '$maxValueExceededError (maximum: ${maxValue.toStringAsFixed(2)})';
    }

    return null;
  }

  /// Validate percentage values (0-100)
  static String? validatePercentage(String? value, {
    bool required = true,
    String? customErrorMessage,
  }) {
    if (value == null || value.trim().isEmpty) {
      return required ? (customErrorMessage ?? requiredFieldError) : null;
    }

    final cleanValue = value.replaceAll(RegExp(r'[^\d.,]'), '').replaceAll(',', '.');
    final parsedValue = double.tryParse(cleanValue);

    if (parsedValue == null) {
      return customErrorMessage ?? invalidNumberError;
    }

    if (parsedValue < 0 || parsedValue > 100) {
      return customErrorMessage ?? invalidPercentageError;
    }

    return null;
  }

  /// Validate tax rate (0-50)
  static String? validateTaxRate(String? value, {
    bool required = true,
    String? customErrorMessage,
  }) {
    if (value == null || value.trim().isEmpty) {
      return required ? (customErrorMessage ?? requiredFieldError) : null;
    }

    final cleanValue = value.replaceAll(RegExp(r'[^\d.,]'), '').replaceAll(',', '.');
    final parsedValue = double.tryParse(cleanValue);

    if (parsedValue == null) {
      return customErrorMessage ?? invalidNumberError;
    }

    if (parsedValue < 0 || parsedValue > 50) {
      return customErrorMessage ?? invalidTaxRateError;
    }

    return null;
  }

  /// Validate integer values (number of children, years, etc.)
  static String? validateInteger(String? value, {
    bool required = true,
    int? minValue,
    int? maxValue,
    String? customErrorMessage,
  }) {
    if (value == null || value.trim().isEmpty) {
      return required ? (customErrorMessage ?? requiredFieldError) : null;
    }

    final parsedValue = int.tryParse(value.trim());

    if (parsedValue == null) {
      return customErrorMessage ?? invalidNumberError;
    }

    if (parsedValue < 0) {
      return customErrorMessage ?? negativeNumberError;
    }

    if (minValue != null && parsedValue < minValue) {
      return customErrorMessage ?? '$minValueError (minimum: $minValue)';
    }

    if (maxValue != null && parsedValue > maxValue) {
      return customErrorMessage ?? '$maxValueExceededError (maximum: $maxValue)';
    }

    return null;
  }

  /// Validate salary amount with specific constraints
  static String? validateSalary(String? value, {
    bool required = true,
    String? customErrorMessage,
  }) {
    return validateMonetaryAmount(
      value,
      required: required,
      minValue: 0,
      maxValue: 1000000, // 1M MAD maximum
      customErrorMessage: customErrorMessage ?? 'Veuillez saisir un salaire valide (0 - 1,000,000 DH)',
    );
  }

  /// Validate revenue amount
  static String? validateRevenue(String? value, {
    bool required = true,
    String? customErrorMessage,
  }) {
    return validateMonetaryAmount(
      value,
      required: required,
      minValue: 0,
      maxValue: 100000000, // 100M MAD maximum
      customErrorMessage: customErrorMessage ?? 'Veuillez saisir un chiffre d\'affaires valide',
    );
  }

  /// Validate number of dependents
  static String? validateDependents(String? value, {
    bool required = false,
    String? customErrorMessage,
  }) {
    return validateInteger(
      value,
      required: required,
      minValue: 0,
      maxValue: 10,
      customErrorMessage: customErrorMessage ?? 'Le nombre de personnes à charge doit être entre 0 et 10',
    );
  }

  /// Validate years of service/seniority
  static String? validateYearsOfService(String? value, {
    bool required = false,
    String? customErrorMessage,
  }) {
    return validateInteger(
      value,
      required: required,
      minValue: 0,
      maxValue: 50,
      customErrorMessage: customErrorMessage ?? 'L\'ancienneté doit être entre 0 et 50 ans',
    );
  }

  /// Clean and parse monetary input
  static double parseMonetaryInput(String input) {
    final cleanValue = input.replaceAll(RegExp(r'[^\d.,]'), '').replaceAll(',', '.');
    return double.tryParse(cleanValue) ?? 0.0;
  }

  /// Clean and parse percentage input
  static double parsePercentageInput(String input) {
    final cleanValue = input.replaceAll(RegExp(r'[^\d.,]'), '').replaceAll(',', '.');
    return double.tryParse(cleanValue) ?? 0.0;
  }

  /// Clean and parse integer input
  static int parseIntegerInput(String input) {
    return int.tryParse(input.trim()) ?? 0;
  }

  /// Format input for display (add thousand separators)
  static String formatMonetaryInput(String input) {
    final value = parseMonetaryInput(input);
    if (value == 0) return '';
    
    final formatter = RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))');
    return value.toStringAsFixed(2).replaceAllMapped(formatter, (Match m) => '${m[1]} ');
  }

  /// Input formatter for monetary fields
  static List<TextInputFormatter> getMonetaryInputFormatters() {
    return [
      FilteringTextInputFormatter.allow(RegExp(r'[0-9.,\s]')),
      TextInputFormatter.withFunction((oldValue, newValue) {
        // Remove any non-numeric characters except comma and dot
        String newText = newValue.text.replaceAll(RegExp(r'[^\d.,]'), '');
        
        // Replace comma with dot for decimal separator
        newText = newText.replaceAll(',', '.');
        
        // Ensure only one decimal point
        final parts = newText.split('.');
        if (parts.length > 2) {
          newText = '${parts[0]}.${parts.sublist(1).join('')}';
        }
        
        // Limit decimal places to 2
        if (parts.length == 2 && parts[1].length > 2) {
          newText = '${parts[0]}.${parts[1].substring(0, 2)}';
        }
        
        return TextEditingValue(
          text: newText,
          selection: TextSelection.collapsed(offset: newText.length),
        );
      }),
    ];
  }

  /// Input formatter for percentage fields
  static List<TextInputFormatter> getPercentageInputFormatters() {
    return [
      FilteringTextInputFormatter.allow(RegExp(r'[0-9.,]')),
      TextInputFormatter.withFunction((oldValue, newValue) {
        String newText = newValue.text.replaceAll(',', '.');
        final value = double.tryParse(newText);
        
        if (value != null && value > 100) {
          return oldValue;
        }
        
        return newValue;
      }),
    ];
  }

  /// Input formatter for integer fields
  static List<TextInputFormatter> getIntegerInputFormatters({int? maxValue}) {
    return [
      FilteringTextInputFormatter.digitsOnly,
      if (maxValue != null)
        TextInputFormatter.withFunction((oldValue, newValue) {
          final value = int.tryParse(newValue.text);
          if (value != null && value > maxValue) {
            return oldValue;
          }
          return newValue;
        }),
    ];
  }
}
