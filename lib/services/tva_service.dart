import 'dart:convert';
import 'package:flutter/services.dart';
import '../models/tva/tva_data.dart';
import '../models/tva/ras_tva_data.dart';
import '../models/tva/calcul_tva_data.dart';
import '../models/tva/nouveautes_tva_data.dart';
import '../models/tva/exercices_tva_data.dart';
import '../models/tva/remboursement_tva_data.dart';

class TvaService {
  static final TvaService _instance = TvaService._internal();
  factory TvaService() => _instance;
  TvaService._internal();

  TvaData? _cachedTvaData2025;
  RasTvaData? _cachedRasTvaData;
  CalculTvaData? _cachedCalculTvaData;
  NouveautesTvaData? _cachedNouveautesTvaData;
  ExercicesTvaData? _cachedExercicesTvaData;
  RemboursementTvaData? _cachedRemboursementTvaData;

  Future<TvaData> getTva2025() async {
    if (_cachedTvaData2025 != null) {
      print('Returning cached TVA 2025 data');
      return _cachedTvaData2025!;
    }

    try {
      print('Loading TVA 2025 data from assets');
      final String jsonString =
          await rootBundle.loadString('assets/tva/tva_2025.json');
      final data = json.decode(jsonString);
      _cachedTvaData2025 = TvaData.fromJson(data);
      print('Successfully loaded TVA 2025 data');
      return _cachedTvaData2025!;
    } catch (e) {
      print('Error loading TVA 2025 data: $e');
      rethrow;
    }
  }

  Future<RasTvaData> getRasTva() async {
    if (_cachedRasTvaData != null) {
      print('Returning cached RAS TVA data');
      return _cachedRasTvaData!;
    }

    try {
      print('Loading RAS TVA data from assets');
      final String jsonString =
          await rootBundle.loadString('assets/tva/ras_tva.json');
      final data = json.decode(jsonString);
      _cachedRasTvaData = RasTvaData.fromJson(data);
      print('Successfully loaded RAS TVA data');
      return _cachedRasTvaData!;
    } catch (e) {
      print('Error loading RAS TVA data: $e');
      rethrow;
    }
  }

  Future<CalculTvaData> getCalculTva() async {
    if (_cachedCalculTvaData != null) {
      print('Returning cached Calcul TVA data');
      return _cachedCalculTvaData!;
    }

    try {
      print('Loading Calcul TVA data from assets');
      final String jsonString =
          await rootBundle.loadString('assets/tva/calcul_tva.json');
      final data = json.decode(jsonString);
      _cachedCalculTvaData = CalculTvaData.fromJson(data);
      print('Successfully loaded Calcul TVA data');
      return _cachedCalculTvaData!;
    } catch (e) {
      print('Error loading Calcul TVA data: $e');
      rethrow;
    }
  }

  Future<NouveautesTvaData> getNouveautesTva() async {
    if (_cachedNouveautesTvaData != null) {
      print('Returning cached Nouveautes TVA data');
      return _cachedNouveautesTvaData!;
    }

    try {
      print('Loading Nouveautes TVA data from assets');
      final String jsonString =
          await rootBundle.loadString('assets/tva/nouveautes_tva.json');
      final data = json.decode(jsonString);
      _cachedNouveautesTvaData = NouveautesTvaData.fromJson(data);
      print('Successfully loaded Nouveautes TVA data');
      return _cachedNouveautesTvaData!;
    } catch (e) {
      print('Error loading Nouveautes TVA data: $e');
      rethrow;
    }
  }

  Future<ExercicesTvaData> getExercicesTva() async {
    if (_cachedExercicesTvaData != null) {
      print('Returning cached Exercices TVA data');
      return _cachedExercicesTvaData!;
    }

    try {
      print('Loading Exercices TVA data from assets');
      final String jsonString =
          await rootBundle.loadString('assets/tva/exercices_tva.json');
      final data = json.decode(jsonString);
      _cachedExercicesTvaData = ExercicesTvaData.fromJson(data);
      print('Successfully loaded Exercices TVA data');
      return _cachedExercicesTvaData!;
    } catch (e) {
      print('Error loading Exercices TVA data: $e');
      rethrow;
    }
  }

  Future<RemboursementTvaData> getRemboursementTvaData() async {
    if (_cachedRemboursementTvaData != null) {
      print('Returning cached Remboursement TVA data');
      return _cachedRemboursementTvaData!;
    }

    try {
      print('Loading Remboursement TVA data from assets');
      final String jsonString =
          await rootBundle.loadString('assets/tva/remboursement_tva_2025.json');
      final data = json.decode(jsonString);
      _cachedRemboursementTvaData = RemboursementTvaData.fromJson(data);
      print('Successfully loaded Remboursement TVA data');
      return _cachedRemboursementTvaData!;
    } catch (e) {
      print('Error loading Remboursement TVA data: $e');
      rethrow;
    }
  }

  void clearCache() {
    print('Clearing TVA data cache');
    _cachedTvaData2025 = null;
    _cachedRasTvaData = null;
    _cachedCalculTvaData = null;
    _cachedNouveautesTvaData = null;
    _cachedExercicesTvaData = null;
    _cachedRemboursementTvaData = null;
  }
}
