import 'dart:convert';
import 'package:flutter/services.dart';

class IsCalculatorService {
  static Future<Map<String, dynamic>> loadIsTaux() async {
    final String jsonString =
        await rootBundle.loadString('assets/is/is_taux_2025.json');
    return json.decode(jsonString);
  }

  static Future<Map<String, dynamic>> loadIsReintegrations() async {
    final String jsonString =
        await rootBundle.loadString('assets/is/is_rein_deduction_2025.json');
    return json.decode(jsonString);
  }

  static double calculateIS(
      double benefit, String sector, Map<String, dynamic> tauxData) {
    final regimes = tauxData['regimes'] as List<dynamic>? ?? [];
    final regime = regimes.firstWhere(
      (r) => r['name'] == sector,
      orElse: () => regimes.first,
    );

    final tranches = regime['tranches'] as List<dynamic>? ?? [];
    double totalTax = 0;
    double remainingBenefit = benefit;

    for (final tranche in tranches) {
      if (tranche == null) continue;

      // Safely convert numeric values
      final num minValue = tranche['min'] is num ? tranche['min'] as num : 0;
      final double min = minValue.toDouble();

      final dynamic maxValue = tranche['max'];
      final double? max = maxValue is num ? maxValue.toDouble() : null;

      final num rateValue = tranche['taux'] is num ? tranche['taux'] as num : 0;
      final double rate = rateValue.toDouble();

      if (remainingBenefit <= 0) break;

      double taxableBenefit;
      if (max == null) {
        // Last tranche
        taxableBenefit = remainingBenefit;
      } else {
        taxableBenefit = remainingBenefit.clamp(0, max - min);
      }

      totalTax += taxableBenefit * (rate / 100);
      remainingBenefit -= taxableBenefit;
    }

    return totalTax;
  }

  static double calculateCM(
    double revenue,
    String revenueType,
    Map<String, dynamic> tauxData,
    bool isExempted,
    bool isFirstYear,
  ) {
    if (isExempted || isFirstYear) return 0;

    final cm = tauxData['cotisation_minimale'] as Map<String, dynamic>? ?? {};
    final rates = cm['taux'] as Map<String, dynamic>? ?? {};

    // Safely get the rate value
    final dynamic rateValue = rates[revenueType];
    final double rate = rateValue is num ? rateValue.toDouble() : 0.0;

    double calculatedCM = revenue * (rate / 100);

    // Safely get the minimum value
    final dynamic minValue = cm['minimum'];
    final double minCM =
        minValue is num ? minValue.toDouble() : 3000.0; // Default to 3000 MAD

    return calculatedCM < minCM ? minCM : calculatedCM;
  }

  static double calculateReintegrations(
    Map<String, dynamic> values,
    Map<String, dynamic> reintegrationData,
  ) {
    double total = 0;
    final categories =
        reintegrationData['reintegrations'] as List<dynamic>? ?? [];

    for (final category in categories) {
      if (category == null) continue;
      final items = category['items'] as List<dynamic>? ?? [];
      for (final item in items) {
        if (item == null) continue;
        final String? id = item['id'] as String?;
        if (id != null && values.containsKey(id)) {
          final dynamic value = values[id];
          if (value == null) continue;

          // Safely convert value to double
          final double numValue = value is num
              ? value.toDouble()
              : double.tryParse(value.toString()) ?? 0.0;

          final String? formula = item['formula'] as String?;

          if (formula != null) {
            // Apply formula if exists
            switch (formula) {
              case 'excess_20000':
                if (numValue > 20000) {
                  total += numValue * 0.5; // 50% of amount exceeding 20,000
                }
                break;
              default:
                total += numValue;
            }
          } else {
            total += numValue;
          }
        }
      }
    }

    return total;
  }

  static double calculateDeductions(
    Map<String, dynamic> values,
    Map<String, dynamic> reintegrationData,
  ) {
    double total = 0;
    final deductions = reintegrationData['deductions'] as List<dynamic>? ?? [];

    for (final deduction in deductions) {
      if (deduction == null) continue;
      final items = deduction['items'] as List<dynamic>? ?? [];
      for (final item in items) {
        if (item == null) continue;
        final String? id = item['id'] as String?;
        if (id != null && values.containsKey(id)) {
          final dynamic value = values[id];
          if (value == null) continue;

          // Safely convert value to double
          final double numValue = value is num
              ? value.toDouble()
              : double.tryParse(value.toString()) ?? 0.0;

          total += numValue;
        }
      }
    }

    return total;
  }
}
